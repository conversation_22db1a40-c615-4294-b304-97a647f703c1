// This icon file is generated automatically.
var XFilled = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "g", "attrs": { "fill-rule": "evenodd" }, "children": [{ "tag": "path", "attrs": { "d": "M823.11 912H200.9A88.9 88.9 0 01112 823.11V200.9A88.9 88.9 0 01200.89 112H823.1A88.9 88.9 0 01912 200.89V823.1A88.9 88.9 0 01823.11 912" } }, { "tag": "path", "attrs": { "d": "M740 735H596.94L286 291h143.06zm-126.01-37.65h56.96L412 328.65h-56.96z", "fill-rule": "nonzero" } }, { "tag": "path", "attrs": { "d": "M331.3 735L491 549.73 470.11 522 286 735zM521 460.39L541.21 489 715 289h-44.67z", "fill-rule": "nonzero" } }] }] }, "name": "x", "theme": "filled" };
export default XFilled;
