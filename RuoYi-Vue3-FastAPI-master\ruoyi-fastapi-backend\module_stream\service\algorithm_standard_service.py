"""
标准化算法配置服务
提供统一的算法配置管理接口
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from module_stream.entity.do.stream_do import SurveillanceStream
from module_stream.entity.do.task_do import SurveillanceTask
from module_stream.entity.vo.algorithm_standard_vo import (
    AlgorithmConfig, AlgorithmConfigRequest, AlgorithmConfigResponse,
    AlgorithmCapability, TaskExecutionRequest, TaskExecutionResponse
)
from module_stream.service.algorithm_config_service import AlgorithmConfigService
from utils.log_util import logger
from exceptions.exception import ServiceException


class AlgorithmStandardService:
    """标准化算法配置服务"""
    
    @classmethod
    async def get_algorithm_capabilities(cls) -> List[AlgorithmCapability]:
        """
        获取所有算法的能力描述（从Custom-Algorithm目录动态加载）

        :return: 算法能力列表
        """
        try:
            import os
            import json
            from pathlib import Path

            capabilities = []
            loaded_algorithms = set()  # 用于去重，避免同一算法在不同平台重复加载

            # Custom-Algorithm目录路径（根据实际路径调整）
            alternative_paths = [
                Path("../Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo"),
                Path("../../Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo"),
                Path("d:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo"),
                Path("Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo"),
                Path("../../../Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo")
            ]

            base_dir = None
            for alt_path in alternative_paths:
                if alt_path.exists():
                    base_dir = alt_path
                    break

            if not base_dir:
                logger.warning("Custom-Algorithm目录不存在，尝试的路径:")
                for path in alternative_paths:
                    logger.warning(f"  - {path.absolute()}")
                return []

            logger.info(f"从目录加载算法: {base_dir}")

            # 优先加载的平台顺序（优先KS988的onnx模型，因为通用性更好）
            platform_priority = ['KS988', 'KS968']

            for platform_name in platform_priority:
                platform_dir = base_dir / platform_name
                if not platform_dir.exists() or not platform_dir.is_dir():
                    continue

                logger.info(f"扫描平台: {platform_name}")

                # 遍历算法目录
                for algorithm_dir in platform_dir.iterdir():
                    if not algorithm_dir.is_dir() or algorithm_dir.name.startswith('.'):
                        continue

                    # 如果算法已经加载过，跳过（避免重复）
                    if algorithm_dir.name in loaded_algorithms:
                        logger.debug(f"算法 {algorithm_dir.name} 已加载，跳过平台 {platform_name}")
                        continue

                    # 验证是否有实际的模型文件
                    if not cls._has_valid_model_files(algorithm_dir, platform_name):
                        logger.warning(f"算法 {algorithm_dir.name} 在平台 {platform_name} 下缺少有效的模型文件，跳过")
                        continue

                    # 查找postprocessor目录下的JSON配置文件
                    postprocessor_dir = algorithm_dir / "postprocessor"
                    if not postprocessor_dir.exists():
                        logger.warning(f"算法 {algorithm_dir.name} 缺少postprocessor目录")
                        continue

                    # 查找算法配置文件（通常与算法目录同名）
                    config_file = postprocessor_dir / f"{algorithm_dir.name}.json"

                    if not config_file.exists():
                        logger.warning(f"算法配置文件不存在: {config_file}")
                        continue

                    try:
                        # 读取算法配置
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config_data = json.load(f)

                        # 解析算法能力
                        capability = cls._parse_algorithm_capability(
                            algorithm_dir.name,
                            config_data,
                            platform_name,
                            algorithm_dir
                        )
                        if capability:
                            capabilities.append(capability)
                            loaded_algorithms.add(algorithm_dir.name)
                            logger.info(f"加载算法能力: {capability.algorithm_id} (平台: {platform_name})")

                    except Exception as e:
                        logger.error(f"解析算法配置失败 {algorithm_dir.name}: {e}")
                        continue

            # 如果没有加载到任何算法，提供一些默认的算法配置
            if not capabilities:
                logger.warning("未找到有效的算法模型文件，使用默认算法配置")
                capabilities = cls._get_default_capabilities()

            logger.info(f"成功加载 {len(capabilities)} 个算法能力描述")
            return capabilities

        except Exception as e:
            logger.error(f"获取算法能力失败: {e}")
            raise ServiceException(message="获取算法能力失败")

    @classmethod
    def _has_valid_model_files(cls, algorithm_dir: Path, platform: str) -> bool:
        """
        检查算法目录是否包含有效的模型文件

        :param algorithm_dir: 算法目录路径
        :param platform: 平台名称
        :return: 是否有有效的模型文件
        """
        try:
            model_dir = algorithm_dir / "model"
            if not model_dir.exists():
                return False

            # 根据平台确定模型文件扩展名
            model_extensions = {
                'KS968': ['.rknn'],
                'KS988': ['.onnx', '.trt', '.engine']
            }

            extensions = model_extensions.get(platform, ['.onnx', '.rknn'])

            # 递归搜索模型文件
            for ext in extensions:
                model_files = list(model_dir.rglob(f"*{ext}"))
                if model_files:
                    logger.debug(f"找到模型文件: {model_files[0]} (算法: {algorithm_dir.name})")
                    return True

            # 检查是否有model.yaml配置文件（表示有模型配置）
            model_yaml = model_dir / "model.yaml"
            if model_yaml.exists():
                logger.debug(f"找到模型配置文件: {model_yaml} (算法: {algorithm_dir.name})")
                return True

            logger.debug(f"算法 {algorithm_dir.name} 在平台 {platform} 下未找到有效的模型文件")
            return False

        except Exception as e:
            logger.error(f"检查模型文件失败 {algorithm_dir.name}: {e}")
            return False

    @classmethod
    def _parse_algorithm_capability(cls, algorithm_id: str, config_data: dict, platform: str = "", algorithm_dir: Path = None) -> Optional[AlgorithmCapability]:
        """
        解析算法配置数据为能力描述

        :param algorithm_id: 算法ID（目录名）
        :param config_data: 配置数据
        :param platform: 平台名称
        :return: 算法能力描述
        """
        try:
            # 从basicParams和renderParams中提取信息
            basic_params = config_data.get("basicParams", {})
            render_params = config_data.get("renderParams", {})

            # 获取基本信息
            reserved_args = basic_params.get("reserved_args", {})
            algorithm_name = reserved_args.get("ch_name", algorithm_id.replace("_", " ").title())

            # 尝试从智驱力元数据文件获取版本信息
            algorithm_version = "1.0.0"  # 默认版本
            try:
                metadata = AlgorithmConfigService.get_algorithm_metadata_from_yaml(algorithm_id, platform)
                if metadata and 'version' in metadata:
                    algorithm_version = metadata['version']
                    logger.debug(f"从元数据获取算法版本: {algorithm_id} -> {algorithm_version}")
            except Exception as e:
                logger.warning(f"无法获取算法 {algorithm_id} 的版本信息: {e}")

            description = f"{algorithm_name} - {platform}平台算法"

            # 从bbox配置中获取支持的几何类型
            bbox_render = render_params.get("bbox", {})
            supports_polygons = "polygons" in bbox_render
            supports_lines = "lines" in bbox_render
            supports_exclusion = False  # 根据polygons的exits属性判断

            if supports_polygons:
                polygon_config = bbox_render.get("polygons", {})
                # 如果polygons的exits是"must"，说明必须要多边形区域
                supports_exclusion = polygon_config.get("exits") in ["must", "optional"]

            # 获取可配置参数
            configurable_params = []
            model_args = render_params.get("model_args", {})
            for model_name, model_config in model_args.items():
                for param_name, param_config in model_config.items():
                    configurable_params.append(f"{model_name}.{param_name}")

            # 添加告警窗口参数
            alert_window = render_params.get("alert_window", {})
            for param_name in alert_window.keys():
                configurable_params.append(f"alert_window.{param_name}")

            # 输入输出格式
            input_format = {"type": "video_stream", "format": "rtsp"}

            # 根据算法类型确定输出格式
            alg_type = basic_params.get("alg_type", "general")
            if "counting" in algorithm_id:
                output_format = {"type": "counting_statistics", "format": "json"}
            elif "intrusion" in algorithm_id or "departure" in algorithm_id:
                output_format = {"type": "detection_events", "format": "json"}
            else:
                output_format = {"type": "detection_result", "format": "json"}

            # 创建默认配置
            default_config = cls._create_default_config(
                algorithm_id, algorithm_name, supports_polygons, supports_lines, supports_exclusion
            )

            return AlgorithmCapability(
                algorithm_id=algorithm_id,  # 不再添加平台前缀，因为已经去重了
                algorithm_name=algorithm_name,
                algorithm_version=algorithm_version,
                description=f"{description} (平台: {platform})",  # 在描述中标注平台信息
                supports_polygons=supports_polygons,
                supports_lines=supports_lines,
                supports_exclusion=supports_exclusion,
                configurable_params=configurable_params,
                default_config=default_config,
                input_format=input_format,
                output_format=output_format
            )

        except Exception as e:
            logger.error(f"解析算法能力失败 {algorithm_id}: {e}")
            return None

    @classmethod
    def _get_default_capabilities(cls) -> List[AlgorithmCapability]:
        """
        获取默认的算法能力列表（当没有找到有效模型时使用）

        :return: 默认算法能力列表
        """
        default_algorithms = [
            {
                "algorithm_id": "person_intrusion",
                "algorithm_name": "人员入侵检测",
                "description": "检测指定区域内的人员入侵行为",
                "supports_polygons": True,
                "supports_lines": False,
                "supports_exclusion": True
            },
            {
                "algorithm_id": "helmet_detection",
                "algorithm_name": "安全帽检测",
                "description": "检测人员是否佩戴安全帽",
                "supports_polygons": True,
                "supports_lines": False,
                "supports_exclusion": False
            },
            {
                "algorithm_id": "fire_detection",
                "algorithm_name": "火灾检测",
                "description": "检测视频中的火焰和烟雾",
                "supports_polygons": True,
                "supports_lines": False,
                "supports_exclusion": True
            },
            {
                "algorithm_id": "car_counting",
                "algorithm_name": "车辆计数",
                "description": "统计通过指定区域的车辆数量",
                "supports_polygons": False,
                "supports_lines": True,
                "supports_exclusion": False
            },
            {
                "algorithm_id": "fall_detection",
                "algorithm_name": "跌倒检测",
                "description": "检测人员跌倒行为",
                "supports_polygons": True,
                "supports_lines": False,
                "supports_exclusion": False
            }
        ]

        capabilities = []
        for alg_data in default_algorithms:
            try:
                # 创建默认配置
                default_config = cls._create_default_config(
                    alg_data["algorithm_id"],
                    alg_data["algorithm_name"],
                    alg_data["supports_polygons"],
                    alg_data["supports_lines"],
                    alg_data["supports_exclusion"],
                    alg_data.get("algorithm_version", "1.0.0")
                )

                capability = AlgorithmCapability(
                    algorithm_id=alg_data["algorithm_id"],
                    algorithm_name=alg_data["algorithm_name"],
                    algorithm_version=alg_data.get("algorithm_version", "1.0.0"),
                    description=f"{alg_data['description']} (默认配置)",
                    supports_polygons=alg_data["supports_polygons"],
                    supports_lines=alg_data["supports_lines"],
                    supports_exclusion=alg_data["supports_exclusion"],
                    configurable_params=["confidence_threshold", "nms_threshold"],
                    default_config=default_config,
                    input_format={"type": "video_stream", "format": "rtsp"},
                    output_format={"type": "detection_result", "format": "json"}
                )
                capabilities.append(capability)

            except Exception as e:
                logger.error(f"创建默认算法配置失败 {alg_data['algorithm_id']}: {e}")
                continue

        return capabilities

    @classmethod
    def _create_default_config(
        cls,
        algorithm_id: str,
        algorithm_name: str,
        supports_polygons: bool,
        supports_lines: bool,
        supports_exclusion: bool,
        algorithm_version: str = "1.0.0"
    ) -> AlgorithmConfig:
        """
        创建默认算法配置

        :param algorithm_id: 算法ID
        :param algorithm_name: 算法名称
        :param supports_polygons: 是否支持多边形
        :param supports_lines: 是否支持线段
        :param supports_exclusion: 是否支持排除区域
        :param algorithm_version: 算法版本
        :return: 默认配置
        """
        from module_stream.entity.vo.algorithm_standard_vo import (
            AlgorithmConfig, ModelParameters, AlertParameters
        )

        return AlgorithmConfig(
            algorithm_id=algorithm_id,
            algorithm_name=algorithm_name,
            algorithm_version=algorithm_version,
            detection_areas=[] if supports_polygons else [],
            exclusion_areas=[] if supports_exclusion else [],
            detection_lines=[] if supports_lines else [],
            model_params=ModelParameters(),
            alert_params=AlertParameters(),
            custom_params={}
        )
    
    @classmethod
    async def save_algorithm_config(
        cls,
        db: AsyncSession,
        request: AlgorithmConfigRequest,
        user_id: int
    ) -> AlgorithmConfigResponse:
        """
        保存算法配置（标准化接口）

        :param db: 数据库会话
        :param request: 配置请求
        :param user_id: 用户ID
        :return: 配置响应
        """
        try:
            # 0. 参数验证
            await cls._validate_request_params(request)

            # 1. 验证视频流是否存在且属于当前用户
            stream_query = select(SurveillanceStream).where(
                and_(
                    SurveillanceStream.stream_id == request.stream_id,
                    SurveillanceStream.user_id == user_id
                )
            )
            stream_result = await db.execute(stream_query)
            stream_info = stream_result.scalar_one_or_none()

            if not stream_info:
                raise ServiceException(message="视频流不存在或无权限访问")

            # 2. 检查是否已存在任务
            task_query = select(SurveillanceTask).where(
                and_(
                    SurveillanceTask.stream_id == request.stream_id,
                    SurveillanceTask.algorithm_id == request.config.algorithm_id,
                    SurveillanceTask.create_by == str(user_id)
                )
            )
            task_result = await db.execute(task_query)
            existing_task = task_result.scalar_one_or_none()

            # 3. 检查任务名称唯一性
            if existing_task:
                # 如果任务名称发生变化，需要检查新名称的唯一性
                if existing_task.task_name != request.task_name:
                    await cls._check_task_name_unique(db, request.task_name, user_id, existing_task.task_id)
            else:
                # 新建任务，检查任务名称是否已存在
                await cls._check_task_name_unique(db, request.task_name, user_id)

            # 3. 使用原始配置数据（避免标准化问题）
            user_config_data = request.config.dict() if hasattr(request.config, 'dict') else request.config

            # 分离不同类型的配置
            algorithm_config = cls._extract_algorithm_config(request.config)
            bbox_config = cls._extract_bbox_config(request.config)
            alert_config = cls._extract_alert_config(request.config)

            # 详细日志记录各个配置字段
            logger.info("=== 任务表配置字段插入调试 ===")
            logger.info(f"1. user_config 字段内容: {json.dumps(user_config_data, ensure_ascii=False, indent=2)}")
            logger.info(f"2. algorithm_config 字段内容: {json.dumps(algorithm_config, ensure_ascii=False, indent=2)}")
            logger.info(f"3. bbox_config 字段内容: {json.dumps(bbox_config, ensure_ascii=False, indent=2)}")
            logger.info(f"4. alert_config 字段内容: {json.dumps(alert_config, ensure_ascii=False, indent=2)}")

            current_time = datetime.now()

            if existing_task:
                # 获取算法的真实名称和版本
                algorithm_full_info = AlgorithmConfigService.get_algorithm_full_info(request.config.algorithm_id)
                actual_algorithm_name = request.config.algorithm_name
                actual_algorithm_version = request.config.algorithm_version

                if algorithm_full_info:
                    actual_algorithm_name = algorithm_full_info.get('algorithm_name', request.config.algorithm_name)
                    actual_algorithm_version = algorithm_full_info.get('version', request.config.algorithm_version)

                # 更新现有任务
                existing_task.task_name = request.task_name
                existing_task.algorithm_name = actual_algorithm_name
                existing_task.algorithm_version = actual_algorithm_version
                existing_task.algorithm_type = request.config.algorithm_id
                existing_task.user_config = user_config_data
                existing_task.algorithm_config = algorithm_config
                existing_task.bbox_config = bbox_config
                existing_task.alert_config = alert_config
                existing_task.update_by = str(user_id)
                existing_task.update_time = current_time
                existing_task.remark = request.remark

                task_id = existing_task.task_id
                logger.info(f"更新检测任务: task_id={task_id}")
            else:
                # 获取算法的真实名称和版本
                algorithm_full_info = AlgorithmConfigService.get_algorithm_full_info(request.config.algorithm_id)
                actual_algorithm_name = request.config.algorithm_name
                actual_algorithm_version = request.config.algorithm_version

                if algorithm_full_info:
                    actual_algorithm_name = algorithm_full_info.get('algorithm_name', request.config.algorithm_name)
                    actual_algorithm_version = algorithm_full_info.get('version', request.config.algorithm_version)

                # 创建新任务
                new_task = SurveillanceTask(
                    task_name=request.task_name,
                    stream_id=request.stream_id,
                    algorithm_id=request.config.algorithm_id,
                    algorithm_name=actual_algorithm_name,
                    algorithm_version=actual_algorithm_version,
                    algorithm_type=request.config.algorithm_id,
                    user_config=user_config_data,
                    algorithm_config=algorithm_config,
                    bbox_config=bbox_config,
                    alert_config=alert_config,
                    status='0',  # 默认停止状态
                    del_flag='0',
                    run_count=0,
                    alert_count=0,
                    error_count=0,
                    create_by=str(user_id),
                    create_time=current_time,
                    update_by=str(user_id),
                    update_time=current_time,
                    remark=request.remark
                )

                db.add(new_task)
                await db.flush()  # 获取task_id
                task_id = new_task.task_id
                logger.info(f"创建检测任务: task_id={task_id}")
            
            await db.commit()
            
            # 4. 返回标准化响应
            return AlgorithmConfigResponse(
                config_id=task_id,  # 现在config_id就是task_id
                task_id=task_id,
                stream_id=request.stream_id,
                algorithm_id=request.config.algorithm_id,
                algorithm_name=request.config.algorithm_name,
                task_name=request.task_name,
                status='configured',
                create_time=current_time.isoformat(),
                update_time=current_time.isoformat()
            )
            
        except Exception as e:
            await db.rollback()
            logger.error(f"保存算法配置失败: {e}")
            raise ServiceException(message=f"保存算法配置失败: {str(e)}")

    @classmethod
    async def _validate_request_params(cls, request: AlgorithmConfigRequest) -> None:
        """
        验证请求参数

        :param request: 配置请求
        :raises ServiceException: 验证失败时抛出异常
        """
        # 验证基本参数
        if not request.stream_id:
            raise ServiceException(message="视频流ID不能为空")

        if not request.config.algorithm_id or request.config.algorithm_id.strip() == '':
            raise ServiceException(message="算法ID不能为空")

        if not request.task_name or request.task_name.strip() == '':
            raise ServiceException(message="任务名称不能为空")

        # 验证算法配置
        config = request.config

        # 检查算法名称，如果为空则使用算法ID
        if not config.algorithm_name or config.algorithm_name.strip() == '':
            config.algorithm_name = config.algorithm_id

        # 检查算法版本，如果为空则尝试获取真实版本
        if not config.algorithm_version or config.algorithm_version.strip() == '':
            # 尝试从智驱力元数据获取真实版本
            try:
                metadata = AlgorithmConfigService.get_algorithm_metadata_from_yaml(config.algorithm_id, "KS968")
                if metadata and 'version' in metadata:
                    config.algorithm_version = metadata['version']
                else:
                    config.algorithm_version = '1.0.0'  # 最后的默认值
            except Exception:
                config.algorithm_version = '1.0.0'  # 最后的默认值

        # 验证模型参数
        if config.model_params:
            if config.model_params.confidence_threshold < 0 or config.model_params.confidence_threshold > 1:
                raise ServiceException(message="置信度阈值必须在0-1之间")

            if config.model_params.nms_threshold < 0 or config.model_params.nms_threshold > 1:
                raise ServiceException(message="NMS阈值必须在0-1之间")

            if config.model_params.input_size <= 0:
                raise ServiceException(message="输入图像尺寸必须大于0")

            if config.model_params.max_detections <= 0:
                raise ServiceException(message="最大检测数量必须大于0")

        # 验证告警参数
        if config.alert_params:
            if config.alert_params.alert_interval <= 0:
                raise ServiceException(message="告警间隔必须大于0")

            if config.alert_params.alert_threshold <= 0:
                raise ServiceException(message="告警阈值必须大于0")

        # 验证检测区域
        for i, area in enumerate(config.detection_areas):
            if not area.name or area.name.strip() == '':
                raise ServiceException(message=f"检测区域{i+1}的名称不能为空")

            if len(area.points) < 3:
                raise ServiceException(message=f"检测区域{i+1}至少需要3个点")

        # 验证检测线段
        for i, line in enumerate(config.detection_lines):
            if not line.name or line.name.strip() == '':
                raise ServiceException(message=f"检测线段{i+1}的名称不能为空")

            if len(line.points) != 2:
                raise ServiceException(message=f"检测线段{i+1}必须有且仅有2个点")

        logger.info("请求参数验证通过")

    @classmethod
    async def _check_task_name_unique(cls, db: AsyncSession, task_name: str, user_id: int, exclude_task_id: int = None) -> None:
        """
        检查任务名称是否唯一

        :param db: 数据库会话
        :param task_name: 任务名称
        :param user_id: 用户ID
        :param exclude_task_id: 排除的任务ID（用于更新时）
        :raises ServiceException: 任务名称已存在时抛出异常
        """
        # 检查任务名称是否已存在
        task_query = select(SurveillanceTask).where(
            and_(
                SurveillanceTask.task_name == task_name,
                SurveillanceTask.del_flag == '0'
            )
        )

        # 如果是更新操作，排除当前任务
        if exclude_task_id:
            task_query = task_query.where(SurveillanceTask.task_id != exclude_task_id)

        task_result = await db.execute(task_query)
        existing_task = task_result.scalar_one_or_none()

        if existing_task:
            # 生成建议的任务名称
            suggested_names = await cls._generate_suggested_task_names(db, task_name, user_id)
            suggestion_text = f"建议使用: {', '.join(suggested_names[:3])}" if suggested_names else ""

            raise ServiceException(
                message=f'任务名称 "{task_name}" 已存在，请使用其他名称。{suggestion_text}'
            )

    @classmethod
    async def _generate_suggested_task_names(cls, db: AsyncSession, base_name: str, user_id: int, max_suggestions: int = 5) -> list:
        """
        生成建议的任务名称

        :param db: 数据库会话
        :param base_name: 基础名称
        :param user_id: 用户ID
        :param max_suggestions: 最大建议数量
        :return: 建议的任务名称列表
        """
        suggestions = []

        # 生成带时间戳的名称
        timestamp = datetime.now().strftime('%m%d_%H%M')
        suggestions.append(f"{base_name}_{timestamp}")

        # 生成带数字后缀的名称
        for i in range(2, max_suggestions + 1):
            candidate_name = f"{base_name}_{i}"

            # 检查候选名称是否已存在
            task_query = select(SurveillanceTask).where(
                and_(
                    SurveillanceTask.task_name == candidate_name,
                    SurveillanceTask.del_flag == '0'
                )
            )
            task_result = await db.execute(task_query)
            existing_task = task_result.scalar_one_or_none()

            if not existing_task:
                suggestions.append(candidate_name)
                if len(suggestions) >= max_suggestions:
                    break

        return suggestions

    @classmethod
    def _extract_algorithm_config(cls, config: AlgorithmConfig) -> Dict:
        """
        提取算法配置参数（模型参数）

        :param config: 算法配置对象
        :return: 算法配置字典
        """
        # 检查是否是智驱力格式的配置
        if hasattr(config, 'algorithm_type') and config.algorithm_type == 'zhiquli':
            # 处理智驱力格式配置
            zhiquli_config = getattr(config, 'zhiquli_config', {})
            model_args = zhiquli_config.get('model_args', {}) if isinstance(zhiquli_config, dict) else {}

            # 从前端传过来的模型参数中提取（如果有的话）
            frontend_model_params = {}
            if hasattr(config, 'model_parameters'):
                model_params = config.model_parameters
                if hasattr(model_params, 'confidence_threshold'):
                    # Pydantic模型对象
                    frontend_model_params = {
                        "confidence_threshold": model_params.confidence_threshold,
                        "nms_threshold": model_params.nms_threshold,
                        "input_size": model_params.input_size,
                        "max_detections": model_params.max_detections
                    }
                elif isinstance(model_params, dict):
                    # 字典对象
                    frontend_model_params = {
                        "confidence_threshold": model_params.get('confidence_threshold', 0.5),
                        "nms_threshold": model_params.get('nms_threshold', 0.5),
                        "input_size": model_params.get('input_size', 640),
                        "max_detections": model_params.get('max_detections', 100)
                    }

            # 从智驱力配置中提取模型参数
            zhiquli_model_params = {
                "confidence_threshold": 0.5,  # 默认值
                "nms_threshold": 0.5,
                "input_size": 640,
                "max_detections": 100
            }

            # 遍历所有模型参数，提取置信度等配置
            for model_name, model_config in model_args.items():
                if isinstance(model_config, dict):
                    if 'conf_thres' in model_config:
                        zhiquli_model_params["confidence_threshold"] = model_config['conf_thres']
                    if 'nms_thres' in model_config:
                        zhiquli_model_params["nms_threshold"] = model_config['nms_thres']

            # 优先使用前端传过来的参数，其次使用智驱力配置中的参数，最后使用默认值
            extracted_params = {
                "confidence_threshold": frontend_model_params.get('confidence_threshold', zhiquli_model_params['confidence_threshold']),
                "nms_threshold": frontend_model_params.get('nms_threshold', zhiquli_model_params['nms_threshold']),
                "input_size": frontend_model_params.get('input_size', zhiquli_model_params['input_size']),
                "max_detections": frontend_model_params.get('max_detections', zhiquli_model_params['max_detections'])
            }

            logger.info(f"前端模型参数: {frontend_model_params}")
            logger.info(f"智驱力模型参数: {zhiquli_model_params}")
            logger.info(f"最终提取的模型参数: {extracted_params}")

            algorithm_config = {
                "model_params": extracted_params,
                "custom_params": zhiquli_config,
                "algorithm_info": {
                    "algorithm_id": config.algorithm_id,
                    "algorithm_name": config.algorithm_name,
                    "algorithm_version": config.algorithm_version
                },
                "version": config.algorithm_version,  # 使用真实的算法版本
                "created_at": datetime.now().isoformat()
            }
        else:
            # 处理标准格式配置
            algorithm_config = {
                "model_params": {
                    "confidence_threshold": config.model_parameters.confidence_threshold,
                    "nms_threshold": config.model_parameters.nms_threshold,
                    "input_size": config.model_parameters.input_size,
                    "max_detections": config.model_parameters.max_detections
                },
                "custom_params": config.custom_params,
                "algorithm_info": {
                    "algorithm_id": config.algorithm_id,
                    "algorithm_name": config.algorithm_name,
                    "algorithm_version": config.algorithm_version
                },
                "version": config.algorithm_version,  # 使用真实的算法版本
                "created_at": datetime.now().isoformat()
            }

        logger.info(f"标准化接口提取到的算法配置: {algorithm_config['model_params']}")
        return algorithm_config

    @classmethod
    def _extract_bbox_config(cls, config: AlgorithmConfig) -> Dict:
        """
        提取区域/线段配置参数

        :param config: 算法配置对象
        :return: 区域配置字典
        """
        # 检查是否是智驱力格式的配置
        if hasattr(config, 'algorithm_type') and config.algorithm_type == 'zhiquli':
            # 处理智驱力格式配置
            detection_areas = []
            detection_lines = []

            # 从前端发送的detection_areas中提取
            if hasattr(config, 'detection_areas') and config.detection_areas:
                for area in config.detection_areas:
                    if hasattr(area, 'dict'):
                        detection_areas.append(area.dict())
                    else:
                        detection_areas.append(area)

            # 从前端发送的detection_lines中提取
            if hasattr(config, 'detection_lines') and config.detection_lines:
                for line in config.detection_lines:
                    if hasattr(line, 'dict'):
                        detection_lines.append(line.dict())
                    else:
                        detection_lines.append(line)

            # 同时从智驱力配置的bbox中提取（如果存在）
            zhiquli_config = getattr(config, 'zhiquli_config', {})
            if isinstance(zhiquli_config, dict) and 'bbox' in zhiquli_config:
                bbox = zhiquli_config['bbox']
                if 'polygons' in bbox and bbox['polygons']:
                    for polygon in bbox['polygons']:
                        detection_areas.append({
                            'id': f"zhiquli_area_{len(detection_areas)}",
                            'name': polygon.get('name', f'智驱力区域{len(detection_areas)+1}'),
                            'points': polygon.get('points', []),
                            'type': 'detection'
                        })
                if 'lines' in bbox and bbox['lines']:
                    for line in bbox['lines']:
                        detection_lines.append({
                            'id': f"zhiquli_line_{len(detection_lines)}",
                            'name': line.get('name', f'智驱力线段{len(detection_lines)+1}'),
                            'points': line.get('points', []),
                            'type': 'line'
                        })

            bbox_config = {
                "detection_areas": detection_areas,
                "exclusion_areas": [],
                "detection_lines": detection_lines,
                "polygons": detection_areas,  # 兼容智驱力格式
                "lines": detection_lines,     # 兼容智驱力格式
                "version": config.algorithm_version,  # 使用真实的算法版本
                "created_at": datetime.now().isoformat()
            }

            logger.info(f"智驱力格式区域配置: 检测区域{len(detection_areas)}个, 检测线段{len(detection_lines)}个")
        else:
            # 处理标准格式配置
            bbox_config = {
                "detection_areas": [area.dict() for area in config.detection_areas],
                "exclusion_areas": [area.dict() for area in config.exclusion_areas],
                "detection_lines": [line.dict() for line in config.detection_lines],
                "version": config.algorithm_version,  # 使用真实的算法版本
                "created_at": datetime.now().isoformat()
            }

        logger.info(f"标准化接口提取到的区域配置: 检测区域{len(bbox_config['detection_areas'])}个, 排除区域{len(bbox_config.get('exclusion_areas', []))}个, 检测线段{len(bbox_config['detection_lines'])}个")
        return bbox_config

    @classmethod
    def _extract_alert_config(cls, config: AlgorithmConfig) -> Dict:
        """
        提取告警配置参数

        :param config: 算法配置对象
        :return: 告警配置字典
        """
        # 检查是否是智驱力格式的配置
        if hasattr(config, 'algorithm_type') and config.algorithm_type == 'zhiquli':
            # 处理智驱力格式配置
            zhiquli_config = getattr(config, 'zhiquli_config', {})
            alert_window = zhiquli_config.get('alert_window', {}) if isinstance(zhiquli_config, dict) else {}

            # 从前端传过来的告警参数中提取（如果有的话）
            frontend_alert_params = {}
            if hasattr(config, 'alert_parameters'):
                alert_params = config.alert_parameters
                if hasattr(alert_params, 'enable_alert'):
                    # Pydantic模型对象
                    frontend_alert_params = {
                        "enable_alert": alert_params.enable_alert,
                        "alert_interval": alert_params.alert_interval,
                        "alert_threshold": alert_params.alert_threshold,
                        "alert_message": alert_params.alert_message
                    }
                elif isinstance(alert_params, dict):
                    # 字典对象
                    frontend_alert_params = {
                        "enable_alert": alert_params.get('enable_alert', True),
                        "alert_interval": alert_params.get('alert_interval', 5),
                        "alert_threshold": alert_params.get('alert_threshold', 1),
                        "alert_message": alert_params.get('alert_message', '检测到异常')
                    }

            # 优先使用前端传过来的参数，其次使用智驱力配置中的参数，最后使用默认值
            alert_params = {
                "enable_alert": frontend_alert_params.get('enable_alert', True),
                "alert_interval": frontend_alert_params.get('alert_interval', alert_window.get('interval', 5)),
                "alert_threshold": frontend_alert_params.get('alert_threshold', alert_window.get('threshold', 1)),
                "alert_message": frontend_alert_params.get('alert_message', zhiquli_config.get('reserved_args', {}).get('sound_text', '检测到异常'))
            }

            alert_config = {
                "alert_params": alert_params,
                "alert_window": alert_window,  # 保留原始智驱力配置
                "version": config.algorithm_version,  # 使用真实的算法版本
                "created_at": datetime.now().isoformat()
            }

            logger.info(f"智驱力格式告警配置: {alert_params}")
            logger.info(f"前端告警参数: {frontend_alert_params}")
            logger.info(f"智驱力alert_window: {alert_window}")
        else:
            # 处理标准格式配置
            alert_config = {
                "alert_params": {
                    "enable_alert": config.alert_parameters.enable_alert,
                    "alert_interval": config.alert_parameters.alert_interval,
                    "alert_threshold": config.alert_parameters.alert_threshold,
                    "alert_message": config.alert_parameters.alert_message
                },
                "version": config.algorithm_version,  # 使用真实的算法版本
                "created_at": datetime.now().isoformat()
            }

        logger.info(f"标准化接口提取到的告警配置: {alert_config['alert_params']}")
        return alert_config

    @classmethod
    def _standardize_config(cls, config: AlgorithmConfig) -> Dict:
        """
        标准化配置数据
        
        :param config: 原始配置
        :return: 标准化后的配置字典
        """
        return {
            "algorithm_info": {
                "algorithm_id": config.algorithm_id,
                "algorithm_name": config.algorithm_name,
                "algorithm_version": config.algorithm_version
            },
            "geometry_config": {
                "detection_areas": [area.dict() for area in config.detection_areas],
                "exclusion_areas": [area.dict() for area in config.exclusion_areas],
                "detection_lines": [line.dict() for line in config.detection_lines]
            },
            "model_params": config.model_parameters.dict(),
            "alert_params": config.alert_parameters.dict(),
            "custom_params": config.custom_params,
            "version": config.algorithm_version,  # 使用真实的算法版本
            "created_at": datetime.now().isoformat()
        }
    

    
    @classmethod
    async def get_algorithm_config(
        cls,
        db: AsyncSession,
        stream_id: int,
        algorithm_id: str,
        user_id: int
    ) -> Optional[AlgorithmConfig]:
        """
        获取算法配置

        :param db: 数据库会话
        :param stream_id: 视频流ID
        :param algorithm_id: 算法ID
        :param user_id: 用户ID
        :return: 算法配置
        """
        try:
            task_query = select(SurveillanceTask).where(
                and_(
                    SurveillanceTask.stream_id == stream_id,
                    SurveillanceTask.algorithm_id == algorithm_id,
                    SurveillanceTask.create_by == str(user_id)
                )
            )
            task_result = await db.execute(task_query)
            task_do = task_result.scalar_one_or_none()

            if not task_do or not task_do.user_config:
                return None

            # 转换为标准格式
            return cls._parse_config_data(task_do.user_config)

        except Exception as e:
            logger.error(f"获取算法配置失败: {e}")
            return None
    
    @classmethod
    def _parse_config_data(cls, config_data: Dict) -> AlgorithmConfig:
        """
        解析配置数据为标准格式
        
        :param config_data: 配置数据字典
        :return: 标准化配置对象
        """
        # 这里需要根据实际的配置数据结构进行解析
        # 暂时返回基本结构
        algorithm_info = config_data.get("algorithm_info", {})
        
        return AlgorithmConfig(
            algorithm_id=algorithm_info.get("algorithm_id", ""),
            algorithm_name=algorithm_info.get("algorithm_name", ""),
            algorithm_version=algorithm_info.get("algorithm_version", "1.0.0"),
            detection_areas=[],  # 需要解析geometry_config
            exclusion_areas=[],
            detection_lines=[],
            custom_params=config_data.get("custom_params", {})
        )
