2025-07-28 09:03:51.331 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 09:03:51.331 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 09:03:51.357 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 09:03:51.357 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 09:03:51.359 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 09:03:51.393 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 09:03:51.424 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 09:03:51.424 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 09:03:57.351 | 309f5b7783f945fe95a3b5b54065efef | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 09:03:57.367 | 4fc22b0d1ba74612a29a77d29f0d2607 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 09:04:00.516 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:04:00.516 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:04:00.517 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:04:00.517 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:04:00.517 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:04:00.517 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:04:00.517 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:04:00.517 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:04:00.521 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:04:00.522 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:04:00.529 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:04:00.529 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:04:00.529 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:04:00.529 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:04:00.531 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:04:00.532 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:04:00.533 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:04:00.533 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:04:00.533 | 8ab7d86ab4354b49b87b4fe4b91b606e | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:04:02.363 | f55acf6cfd684d9f9dd0cbcadc68a6f2 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:68 - 获取成功
2025-07-28 09:04:04.815 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:04:04.815 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:04:04.815 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:04:04.815 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:04:04.816 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:04:04.816 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:04:04.816 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:04:04.816 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:04:04.816 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:04:04.819 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:04:04.819 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:04:04.820 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:04:04.820 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:04:04.821 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:04:04.822 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:04:04.822 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:04:04.822 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:04:04.823 | 7e45e24d6e6440d88bdb9a0c4f961089 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:04:06.195 | 6cdeeb0888c14c26bf11026a38c88179 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:68 - 获取成功
2025-07-28 09:04:11.688 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:95 - 检查告警记录 946: 实体对象 = True
2025-07-28 09:04:11.689 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:187 - 成功删除截图文件: uploads\alerts\20250728\alert_12_8_20250728_085504_475.jpg
2025-07-28 09:04:11.689 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:103 - 成功删除告警截图: /uploads/alerts/20250728/alert_12_8_20250728_085504_475.jpg
2025-07-28 09:04:11.690 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:115 - 成功删除告警记录 946
2025-07-28 09:04:11.692 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:95 - 检查告警记录 945: 实体对象 = True
2025-07-28 09:04:11.692 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:187 - 成功删除截图文件: uploads\alerts\20250728\alert_12_8_20250728_085503_156.jpg
2025-07-28 09:04:11.692 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:103 - 成功删除告警截图: /uploads/alerts/20250728/alert_12_8_20250728_085503_156.jpg
2025-07-28 09:04:11.693 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:115 - 成功删除告警记录 945
2025-07-28 09:04:11.694 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:95 - 检查告警记录 944: 实体对象 = True
2025-07-28 09:04:11.695 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:187 - 成功删除截图文件: uploads\alerts\20250728\alert_12_8_20250728_085501_848.jpg
2025-07-28 09:04:11.695 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:103 - 成功删除告警截图: /uploads/alerts/20250728/alert_12_8_20250728_085501_848.jpg
2025-07-28 09:04:11.696 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:115 - 成功删除告警记录 944
2025-07-28 09:04:11.697 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:95 - 检查告警记录 943: 实体对象 = True
2025-07-28 09:04:11.697 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:187 - 成功删除截图文件: uploads\alerts\20250728\alert_12_8_20250728_085500_477.jpg
2025-07-28 09:04:11.697 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:103 - 成功删除告警截图: /uploads/alerts/20250728/alert_12_8_20250728_085500_477.jpg
2025-07-28 09:04:11.698 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:115 - 成功删除告警记录 943
2025-07-28 09:04:11.699 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:95 - 检查告警记录 942: 实体对象 = True
2025-07-28 09:04:11.700 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:187 - 成功删除截图文件: uploads\alerts\20250728\alert_12_8_20250728_085459_248.jpg
2025-07-28 09:04:11.700 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:103 - 成功删除告警截图: /uploads/alerts/20250728/alert_12_8_20250728_085459_248.jpg
2025-07-28 09:04:11.702 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:115 - 成功删除告警记录 942
2025-07-28 09:04:11.703 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:95 - 检查告警记录 941: 实体对象 = True
2025-07-28 09:04:11.703 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:187 - 成功删除截图文件: uploads\alerts\20250728\alert_12_8_20250728_085458_044.jpg
2025-07-28 09:04:11.703 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:103 - 成功删除告警截图: /uploads/alerts/20250728/alert_12_8_20250728_085458_044.jpg
2025-07-28 09:04:11.704 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:115 - 成功删除告警记录 941
2025-07-28 09:04:11.705 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:95 - 检查告警记录 940: 实体对象 = True
2025-07-28 09:04:11.706 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:187 - 成功删除截图文件: uploads\alerts\20250728\alert_12_8_20250728_085456_850.jpg
2025-07-28 09:04:11.706 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:103 - 成功删除告警截图: /uploads/alerts/20250728/alert_12_8_20250728_085456_850.jpg
2025-07-28 09:04:11.707 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:115 - 成功删除告警记录 940
2025-07-28 09:04:11.708 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:95 - 检查告警记录 939: 实体对象 = True
2025-07-28 09:04:11.708 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:187 - 成功删除截图文件: uploads\alerts\20250728\alert_12_8_20250728_085455_686.jpg
2025-07-28 09:04:11.709 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:103 - 成功删除告警截图: /uploads/alerts/20250728/alert_12_8_20250728_085455_686.jpg
2025-07-28 09:04:11.709 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.service.alert_service:delete_alert_services:115 - 成功删除告警记录 939
2025-07-28 09:04:11.713 | 169e7d1f92f94b46a3c660b0564d5e33 | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:116 - 成功删除8条记录，删除8个截图文件
2025-07-28 09:04:11.740 | 46d0dcf4ae9248ff9ebeae3fc2114f1f | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:68 - 获取成功
2025-07-28 09:04:13.098 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:04:13.098 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:04:13.098 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:04:13.098 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:04:13.098 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:04:13.098 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:04:13.098 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:04:13.098 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:04:13.099 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:04:13.102 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:04:13.102 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:04:13.102 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:04:13.102 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:04:13.103 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:04:13.104 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:04:13.105 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:04:13.105 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:04:13.105 | d032f383a9ef4fa38bfe7920c36b55bf | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:04:14.124 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:04:14.125 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:04:14.125 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:04:14.125 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:04:14.125 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:04:14.125 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:04:14.126 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:04:14.126 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:04:14.126 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:04:14.126 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:04:14.129 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:04:14.129 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:04:14.129 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:04:14.129 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:04:14.130 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:04:14.131 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:04:14.132 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:04:14.132 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:04:14.132 | e459b0db2fa943d28c374d7a25e6f7d6 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:04:15.965 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:start_task:202 - 开始启动任务: 12
2025-07-28 09:04:15.967 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:506 - 加载用户配置: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 09:04:15.967 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:512 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-28 09:04:15.968 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:518 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-28 09:04:15.968 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:524 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-28 09:04:15.968 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:530 - 成功加载数据库配置，包含字段: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version', 'version', 'created_at', 'model_params', 'algorithm_info', 'alert_params']
2025-07-28 09:04:15.968 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:574 - 验证模型初始化 - 添加YOLOv5路径: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master
2025-07-28 09:04:15.969 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:589 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-28 09:04:15.969 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:594 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master\\utils', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition\\RuoYi-Vue3-FastAPI-master\\ruoyi-fastapi-backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs']
2025-07-28 09:04:15.969 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:595 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-28 09:04:15.969 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:599 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 09:04:17.185 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:620 - 验证模型初始化 - 成功导入智驱力模型
2025-07-28 09:04:17.185 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:632 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:04:19.423 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:644 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-28 09:04:19.424 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:645 -    - 设备: cuda
2025-07-28 09:04:19.424 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:646 -    - 图像尺寸: 640
2025-07-28 09:04:19.424 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:647 -    - 置信度阈值: 0.25
2025-07-28 09:04:19.424 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:648 -    - NMS阈值: 0.45
2025-07-28 09:04:19.426 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:661 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-28 09:04:19.439 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:start_monitor_stream:3127 - 任务 12 的监控流已启动
2025-07-28 09:04:19.441 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_cache_task_config:110 - 任务12配置已缓存
2025-07-28 09:04:19.441 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:709 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-28 09:04:19.442 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:739 - 成功预导入YOLOv5 utils模块
2025-07-28 09:04:19.442 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:745 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 09:04:19.442 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:761 - 重新加载模块: zql_detect
2025-07-28 09:04:19.443 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:761 - 重新加载模块: model
2025-07-28 09:04:19.443 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:765 - 成功导入智驱力模型
2025-07-28 09:04:19.443 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:777 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.25, 'nms_thres': 0.45}
2025-07-28 09:04:19.555 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:790 - 智驱力模型初始化成功
2025-07-28 09:04:19.555 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:791 -    - 设备: cuda
2025-07-28 09:04:19.555 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:792 -    - 图像尺寸: 640
2025-07-28 09:04:19.555 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:793 -    - 置信度阈值: 0.25
2025-07-28 09:04:19.555 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:794 -    - NMS阈值: 0.45
2025-07-28 09:04:19.555 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:807 - 智驱力后处理器初始化成功
2025-07-28 09:04:30.962 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:30.962 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 20
2025-07-28 09:04:30.962 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [393, 454, 529, 580]}
2025-07-28 09:04:30.963 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [393, 454, 529, 580]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [576, 261, 664, 346]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 500, 108, 657]}]
2025-07-28 09:04:30.963 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:30.963 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:30.963 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:30.963 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:30.965 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:30.965 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:30.965 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:30.965 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 15 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:30.965 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:30.965 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [393, 454, 529, 580], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:30.965 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:30.965 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 15 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [393, 454, 529, 580], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [576, 261, 664, 346], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 500, 108, 657], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [286, 297, 403, 399], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [451, 209, 505, 257], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [67, 556, 290, 669], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [557, 202, 614, 251], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [441, 277, 522, 352], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.74, 'name': 'car', 'ch_name': 'car', 'xyxy': [479, 174, 527, 213], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [396, 174, 444, 215], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [130, 319, 246, 416], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.65, 'name': 'car', 'ch_name': 'car', 'xyxy': [59, 183, 122, 225], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.63, 'name': 'car', 'ch_name': 'car', 'xyxy': [553, 157, 593, 188], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.58, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1238, 227], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.55, 'name': 'car', 'ch_name': 'car', 'xyxy': [129, 187, 197, 233], 'track_id': 14, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:30.965274', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664670.9652798}
2025-07-28 09:04:30.970 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:start_task:250 - 任务 12 启动成功，包括实时监控流
2025-07-28 09:04:30.970 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-28 09:04:30.982 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:04:30.982 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:04:30.982 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:04:30.982 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:04:30.983 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:04:30.983 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:04:30.983 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:04:30.983 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:04:30.983 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:04:30.984 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:04:30.986 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:04:30.986 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:04:30.987 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:04:30.987 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:04:30.987 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:04:30.987 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:04:30.987 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:04:30.988 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:04:30.988 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:04:30.989 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:04:30.989 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:04:30.989 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:04:30.989 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:04:30.991 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:04:30.992 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:04:30.992 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:04:30.993 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:04:30.993 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:04:30.993 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:04:30.994 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:04:30.994 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:04:30.994 | 6d72f6e0911a4b71ac3aeebc7ac4917c | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:04:30.995 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:04:30.996 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:04:30.996 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:04:30.996 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:04:30.996 | ca49f597d1fd4994a95f3b93650b6050 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:04:31.003 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-28 09:04:31.003 |  | INFO     | module_stream.service.task_execution_service:add_monitor_client:3184 - 客户端 d28c3efb-ca9d-4547-b887-c094784e68f6 已连接到任务 12 的监控流
2025-07-28 09:04:31.003 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 d28c3efb-ca9d-4547-b887-c094784e68f6 连接到任务 12 的监控流 (无认证模式)
2025-07-28 09:04:32.033 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:32.034 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 22
2025-07-28 09:04:32.034 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [404, 432, 529, 553]}
2025-07-28 09:04:32.034 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [404, 432, 529, 553]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [574, 252, 657, 329]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 494, 112, 651]}]
2025-07-28 09:04:32.034 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:32.034 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:32.035 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:32.035 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:32.036 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:32.037 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:32.037 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:32.037 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 22 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:32.037 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:32.037 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [404, 432, 529, 553], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:32.037 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:32.037 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 22 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [404, 432, 529, 553], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [574, 252, 657, 329], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 494, 112, 651], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [295, 294, 401, 391], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [448, 204, 502, 253], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [69, 533, 303, 667], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [556, 195, 612, 247], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [443, 268, 520, 346], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.75, 'name': 'car', 'ch_name': 'car', 'xyxy': [480, 172, 527, 207], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.66, 'name': 'car', 'ch_name': 'car', 'xyxy': [398, 170, 445, 211], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [133, 316, 246, 416], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [49, 186, 112, 230], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [551, 154, 590, 185], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1238, 227], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [119, 191, 189, 237], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.58, 'name': 'car', 'ch_name': 'car', 'xyxy': [446, 113, 470, 136], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [222, 228, 328, 315], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [645, 613, 818, 670], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.48, 'name': 'car', 'ch_name': 'car', 'xyxy': [540, 115, 574, 151], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.46, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 120, 516, 146], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [197, 116, 231, 142], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [527, 106, 551, 126], 'track_id': 21, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:32.036887', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664672.0368931}
2025-07-28 09:04:32.045 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:04:32.046 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:04:32.046 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:04:32.046 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:04:32.046 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:04:32.047 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:04:32.047 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:04:32.047 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:04:32.047 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:04:32.049 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:04:32.049 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:04:32.049 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:04:32.049 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:04:32.049 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:04:32.049 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:04:32.050 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:04:32.050 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:04:32.050 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:04:32.051 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:04:33.069 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:33.070 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 22
2025-07-28 09:04:33.070 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [111, 504, 314, 660]}
2025-07-28 09:04:33.070 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [111, 504, 314, 660]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [410, 398, 533, 516]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 486, 119, 643]}]
2025-07-28 09:04:33.070 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:33.070 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:33.070 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:33.070 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:33.072 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:33.072 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:33.072 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:33.072 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 24 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:33.072 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:33.072 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [410, 398, 533, 516], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:33.073 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:33.073 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 24 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [410, 398, 533, 516], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [574, 241, 649, 310], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 486, 119, 643], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [306, 278, 406, 373], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [445, 196, 497, 243], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [111, 504, 314, 660], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.75, 'name': 'car', 'ch_name': 'car', 'xyxy': [556, 192, 607, 237], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [447, 253, 519, 316], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [481, 170, 527, 204], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [402, 164, 447, 205], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [136, 311, 251, 408], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [30, 190, 97, 240], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.63, 'name': 'car', 'ch_name': 'car', 'xyxy': [548, 152, 588, 180], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1238, 227], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.74, 'name': 'car', 'ch_name': 'car', 'xyxy': [101, 192, 176, 246], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [448, 111, 471, 136], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.56, 'name': 'car', 'ch_name': 'car', 'xyxy': [224, 241, 317, 313], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [635, 565, 821, 671], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [539, 114, 574, 149], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.42, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 118, 515, 140], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [197, 116, 231, 142], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [527, 106, 551, 126], 'track_id': 21, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.4, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [344, 592, 498, 670], 'track_id': 22, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [242, 223, 350, 298], 'track_id': 23, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:33.072381', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664673.0723867}
2025-07-28 09:04:33.074 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:04:33.074 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:04:33.074 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:04:33.075 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:04:33.076 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:04:33.076 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:04:33.076 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:04:33.076 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:04:33.077 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:04:33.078 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:04:33.079 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:04:33.079 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:04:33.079 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:04:33.079 | 8be1ba4beb8649baa32da0ac85c05ef0 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:04:33.080 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:04:33.080 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:04:33.080 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:04:33.080 | 3f06a7d3572648968fa4dec0f1ad7274 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:04:33.085 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-28 09:04:33.086 |  | INFO     | module_stream.service.task_execution_service:add_monitor_client:3184 - 客户端 a6f27892-ee14-49e6-8042-e1b4044cd8f7 已连接到任务 12 的监控流
2025-07-28 09:04:33.086 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 a6f27892-ee14-49e6-8042-e1b4044cd8f7 连接到任务 12 的监控流 (无认证模式)
2025-07-28 09:04:35.114 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:35.115 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 24
2025-07-28 09:04:35.115 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [134, 484, 325, 644]}
2025-07-28 09:04:35.115 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [134, 484, 325, 644]}, {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [630, 536, 815, 671]}, {'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [418, 379, 529, 497]}]
2025-07-28 09:04:35.115 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:35.115 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:35.115 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:35.115 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:35.117 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:35.117 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:35.118 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:35.118 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 26 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:35.118 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:35.118 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [418, 379, 529, 497], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:35.118 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:35.118 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 26 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [418, 379, 529, 497], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [572, 235, 646, 299], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.74, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 484, 123, 631], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [310, 273, 406, 360], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.78, 'name': 'car', 'ch_name': 'car', 'xyxy': [443, 191, 494, 235], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [134, 484, 325, 644], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [555, 188, 606, 230], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [447, 243, 516, 302], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.58, 'name': 'car', 'ch_name': 'car', 'xyxy': [483, 166, 526, 201], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [405, 160, 448, 201], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [139, 304, 253, 404], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [20, 194, 86, 245], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [547, 151, 585, 179], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1238, 227], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [87, 205, 163, 253], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.26, 'name': 'car', 'ch_name': 'car', 'xyxy': [446, 107, 472, 133], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.55, 'name': 'car', 'ch_name': 'car', 'xyxy': [224, 237, 318, 313], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [630, 536, 815, 671], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.46, 'name': 'car', 'ch_name': 'car', 'xyxy': [539, 115, 573, 149], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.43, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 116, 516, 139], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [226, 149, 263, 177], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [527, 106, 551, 126], 'track_id': 21, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.46, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [353, 559, 505, 670], 'track_id': 22, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [242, 223, 350, 298], 'track_id': 23, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [351, 558, 506, 670], 'track_id': 24, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 223, 348, 295], 'track_id': 25, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:35.117815', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664675.117821}
2025-07-28 09:04:37.144 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:37.144 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 24
2025-07-28 09:04:37.144 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.91, 'name': 'car', 'ch_name': 'car', 'xyxy': [147, 469, 332, 613]}
2025-07-28 09:04:37.145 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.91, 'name': 'car', 'ch_name': 'car', 'xyxy': [147, 469, 332, 613]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [625, 510, 799, 653]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [424, 371, 529, 472]}]
2025-07-28 09:04:37.145 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:37.145 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:37.145 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:37.145 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:37.147 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:37.147 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:37.148 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:37.148 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 28 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:37.148 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:37.148 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [424, 371, 529, 472], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:37.148 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:37.148 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 28 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [424, 371, 529, 472], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [569, 227, 640, 290], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 478, 125, 623], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [317, 266, 412, 358], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [443, 187, 492, 228], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.91, 'name': 'car', 'ch_name': 'car', 'xyxy': [147, 469, 332, 613], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [553, 184, 605, 222], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.75, 'name': 'car', 'ch_name': 'car', 'xyxy': [450, 237, 515, 298], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.63, 'name': 'car', 'ch_name': 'car', 'xyxy': [482, 166, 525, 199], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.27, 'name': 'car', 'ch_name': 'car', 'xyxy': [408, 156, 452, 197], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [145, 304, 254, 400], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [16, 198, 74, 249], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [543, 147, 580, 172], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1238, 227], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [73, 209, 150, 261], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [448, 107, 472, 132], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [224, 233, 318, 316], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [625, 510, 799, 653], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [539, 110, 573, 144], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.46, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 113, 515, 137], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [219, 150, 260, 181], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [527, 106, 551, 126], 'track_id': 21, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [360, 528, 509, 670], 'track_id': 22, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [242, 223, 350, 298], 'track_id': 23, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [351, 558, 506, 670], 'track_id': 24, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 223, 348, 295], 'track_id': 25, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [784, 169, 890, 207], 'track_id': 26, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.27, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [408, 156, 452, 194], 'track_id': 27, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:37.147666', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664677.1476717}
2025-07-28 09:04:39.186 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:39.186 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 21
2025-07-28 09:04:39.186 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.92, 'name': 'car', 'ch_name': 'car', 'xyxy': [167, 450, 340, 591]}
2025-07-28 09:04:39.186 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.92, 'name': 'car', 'ch_name': 'car', 'xyxy': [167, 450, 340, 591]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 471, 131, 616]}, {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [427, 355, 530, 455]}]
2025-07-28 09:04:39.186 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:39.186 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:39.187 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:39.187 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:39.189 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:39.189 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:39.189 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:39.189 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 33 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:39.189 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:39.189 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [427, 355, 530, 455], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:39.189 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:39.189 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 33 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [427, 355, 530, 455], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [568, 221, 637, 284], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 471, 131, 616], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [321, 259, 410, 352], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [440, 184, 488, 223], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.92, 'name': 'car', 'ch_name': 'car', 'xyxy': [167, 450, 340, 591], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [552, 180, 601, 219], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [450, 226, 514, 288], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [482, 164, 526, 197], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.42, 'name': 'car', 'ch_name': 'car', 'xyxy': [410, 154, 453, 191], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [147, 304, 257, 397], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [16, 198, 74, 249], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.53, 'name': 'car', 'ch_name': 'car', 'xyxy': [539, 145, 577, 170], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1238, 226], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [73, 209, 150, 261], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [451, 104, 474, 130], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [224, 233, 318, 316], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [620, 486, 783, 639], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [539, 107, 569, 138], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.46, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 113, 515, 137], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [219, 150, 260, 181], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [527, 106, 551, 126], 'track_id': 21, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [360, 528, 509, 670], 'track_id': 22, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [242, 223, 350, 298], 'track_id': 23, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [351, 558, 506, 670], 'track_id': 24, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 223, 348, 295], 'track_id': 25, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [784, 169, 890, 207], 'track_id': 26, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.27, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [408, 156, 452, 194], 'track_id': 27, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [370, 500, 509, 668], 'track_id': 28, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [56, 217, 145, 271], 'track_id': 29, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [227, 229, 320, 312], 'track_id': 30, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [778, 169, 882, 208], 'track_id': 31, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 114, 515, 137], 'track_id': 32, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:39.189213', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664679.1892188}
2025-07-28 09:04:41.222 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:41.222 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 21
2025-07-28 09:04:41.222 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [185, 436, 351, 567]}
2025-07-28 09:04:41.222 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [185, 436, 351, 567]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 469, 136, 610]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [615, 460, 769, 604]}]
2025-07-28 09:04:41.222 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:41.223 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:41.223 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:41.223 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:41.225 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:41.225 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:41.225 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:41.225 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 37 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:41.225 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:41.225 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [435, 344, 530, 438], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:41.226 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:41.226 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 37 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [435, 344, 530, 438], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.75, 'name': 'car', 'ch_name': 'car', 'xyxy': [567, 217, 632, 277], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 469, 136, 610], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [324, 255, 412, 329], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [440, 178, 486, 218], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [185, 436, 351, 567], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [551, 177, 599, 216], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [452, 220, 514, 284], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.46, 'name': 'car', 'ch_name': 'car', 'xyxy': [485, 158, 524, 191], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [411, 151, 454, 188], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.78, 'name': 'car', 'ch_name': 'car', 'xyxy': [148, 303, 257, 396], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [39, 221, 132, 278], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.56, 'name': 'car', 'ch_name': 'car', 'xyxy': [538, 145, 574, 168], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.58, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1238, 226], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [73, 209, 150, 261], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [451, 103, 475, 127], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [224, 233, 318, 316], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [615, 460, 769, 604], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.46, 'name': 'car', 'ch_name': 'car', 'xyxy': [537, 106, 569, 140], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.46, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 113, 515, 137], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [219, 150, 260, 181], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [527, 106, 551, 126], 'track_id': 21, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [360, 528, 509, 670], 'track_id': 22, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [242, 223, 350, 298], 'track_id': 23, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [351, 558, 506, 670], 'track_id': 24, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 223, 348, 295], 'track_id': 25, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [784, 169, 890, 207], 'track_id': 26, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.27, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [408, 156, 452, 194], 'track_id': 27, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [370, 500, 509, 668], 'track_id': 28, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [56, 217, 145, 271], 'track_id': 29, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [227, 229, 320, 312], 'track_id': 30, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [778, 169, 882, 208], 'track_id': 31, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 114, 515, 137], 'track_id': 32, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 111, 515, 135], 'track_id': 33, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.67, 'name': 'car', 'ch_name': 'car', 'xyxy': [231, 228, 318, 307], 'track_id': 34, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [378, 474, 513, 663], 'track_id': 35, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [774, 171, 879, 208], 'track_id': 36, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:41.225403', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664681.2254093}
2025-07-28 09:04:42.234 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:135 - 客户端 a6f27892-ee14-49e6-8042-e1b4044cd8f7 主动断开连接
2025-07-28 09:04:42.234 |  | INFO     | module_stream.service.task_execution_service:remove_monitor_client:3197 - 客户端 a6f27892-ee14-49e6-8042-e1b4044cd8f7 已断开任务 12 的监控流
2025-07-28 09:04:42.235 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:164 - 客户端 a6f27892-ee14-49e6-8042-e1b4044cd8f7 连接已清理
2025-07-28 09:04:42.252 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:42.253 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 25
2025-07-28 09:04:42.253 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [201, 419, 357, 543]}
2025-07-28 09:04:42.253 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [201, 419, 357, 543]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [387, 450, 516, 621]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [611, 438, 756, 571]}]
2025-07-28 09:04:42.253 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:42.253 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:42.253 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:42.253 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:42.255 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:42.255 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:42.255 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:42.255 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 42 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:42.255 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:42.255 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [439, 330, 531, 421], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:42.256 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:42.256 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 42 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [439, 330, 531, 421], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [565, 212, 630, 267], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [1, 463, 140, 605], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [330, 249, 417, 321], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [440, 175, 484, 212], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [201, 419, 357, 543], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.75, 'name': 'car', 'ch_name': 'car', 'xyxy': [551, 174, 595, 213], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [453, 213, 512, 269], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.43, 'name': 'car', 'ch_name': 'car', 'xyxy': [487, 155, 524, 187], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [414, 148, 456, 183], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [149, 303, 257, 395], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [22, 228, 119, 286], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.6, 'name': 'car', 'ch_name': 'car', 'xyxy': [537, 140, 573, 167], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1238, 226], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [73, 209, 150, 261], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.31, 'name': 'car', 'ch_name': 'car', 'xyxy': [451, 103, 475, 125], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [232, 228, 319, 305], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [611, 438, 756, 571], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 105, 568, 140], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 109, 514, 133], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [219, 150, 260, 181], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 116, 573, 162], 'track_id': 21, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [360, 528, 509, 670], 'track_id': 22, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [242, 223, 350, 298], 'track_id': 23, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [351, 558, 506, 670], 'track_id': 24, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 223, 348, 295], 'track_id': 25, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [784, 169, 890, 207], 'track_id': 26, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.27, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [408, 156, 452, 194], 'track_id': 27, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [370, 500, 509, 668], 'track_id': 28, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [56, 217, 145, 271], 'track_id': 29, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [227, 229, 320, 312], 'track_id': 30, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [778, 169, 882, 208], 'track_id': 31, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 114, 515, 137], 'track_id': 32, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 111, 515, 135], 'track_id': 33, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.67, 'name': 'car', 'ch_name': 'car', 'xyxy': [231, 228, 318, 307], 'track_id': 34, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [378, 474, 513, 663], 'track_id': 35, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [774, 171, 879, 208], 'track_id': 36, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [387, 450, 516, 621], 'track_id': 37, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 632, 180, 674], 'track_id': 38, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [433, 164, 482, 198], 'track_id': 39, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [770, 171, 871, 208], 'track_id': 40, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [204, 157, 250, 189], 'track_id': 41, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:42.255439', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664682.255445}
2025-07-28 09:04:42.257 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:135 - 客户端 d28c3efb-ca9d-4547-b887-c094784e68f6 主动断开连接
2025-07-28 09:04:42.257 |  | INFO     | module_stream.service.task_execution_service:remove_monitor_client:3197 - 客户端 d28c3efb-ca9d-4547-b887-c094784e68f6 已断开任务 12 的监控流
2025-07-28 09:04:42.257 |  | INFO     | module_stream.service.task_execution_service:remove_monitor_client:3201 - 任务 12 没有监控客户端，保持监控流运行
2025-07-28 09:04:42.257 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:164 - 客户端 d28c3efb-ca9d-4547-b887-c094784e68f6 连接已清理
2025-07-28 09:04:42.264 | b8c6d96346cb48e09397f0bc5f91ecb9 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 09:04:42.275 | 67c07beb7a6d4716b970d794355c0541 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 09:04:42.384 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:42.384 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 23
2025-07-28 09:04:42.384 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [443, 318, 533, 406]}
2025-07-28 09:04:42.385 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [443, 318, 533, 406]}, {'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [214, 402, 362, 527]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [610, 420, 744, 539]}]
2025-07-28 09:04:42.385 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:42.385 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:42.385 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:42.385 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:42.388 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:42.388 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:42.388 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:42.388 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 48 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:42.388 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:42.388 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [443, 318, 533, 406], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:42.388 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:42.388 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 48 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [443, 318, 533, 406], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [564, 206, 626, 258], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [1, 457, 144, 603], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [334, 242, 416, 317], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [440, 170, 484, 207], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [214, 402, 362, 527], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [550, 170, 595, 206], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [457, 209, 512, 260], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 153, 524, 185], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [416, 143, 458, 180], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [151, 302, 260, 394], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [21, 236, 104, 297], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.58, 'name': 'car', 'ch_name': 'car', 'xyxy': [534, 138, 571, 167], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1238, 226], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [73, 209, 150, 261], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [451, 102, 476, 122], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [232, 228, 319, 305], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [610, 420, 744, 539], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 105, 564, 130], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 109, 514, 133], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [219, 150, 260, 181], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 116, 573, 162], 'track_id': 21, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [360, 528, 509, 670], 'track_id': 22, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [242, 223, 350, 298], 'track_id': 23, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [351, 558, 506, 670], 'track_id': 24, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 223, 348, 295], 'track_id': 25, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [784, 169, 890, 207], 'track_id': 26, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.27, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [408, 156, 452, 194], 'track_id': 27, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [370, 500, 509, 668], 'track_id': 28, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [56, 217, 145, 271], 'track_id': 29, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [227, 229, 320, 312], 'track_id': 30, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [778, 169, 882, 208], 'track_id': 31, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 114, 515, 137], 'track_id': 32, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 111, 515, 135], 'track_id': 33, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.67, 'name': 'car', 'ch_name': 'car', 'xyxy': [231, 228, 318, 307], 'track_id': 34, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [378, 474, 513, 663], 'track_id': 35, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [774, 171, 879, 208], 'track_id': 36, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [387, 450, 516, 621], 'track_id': 37, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 632, 180, 674], 'track_id': 38, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [433, 164, 482, 198], 'track_id': 39, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [770, 171, 871, 208], 'track_id': 40, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [204, 157, 250, 189], 'track_id': 41, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [396, 426, 516, 589], 'track_id': 42, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.75, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 600, 230, 674], 'track_id': 43, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.63, 'name': 'car', 'ch_name': 'car', 'xyxy': [236, 228, 319, 301], 'track_id': 44, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [763, 173, 864, 206], 'track_id': 45, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 107, 514, 131], 'track_id': 46, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [539, 156, 581, 201], 'track_id': 47, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:42.388126', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664682.388132}
2025-07-28 09:04:42.398 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:04:42.398 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:04:42.399 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:04:42.399 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:04:42.399 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:04:42.399 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:04:42.399 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:04:42.400 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:04:42.400 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:04:42.402 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:04:42.402 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:04:42.403 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:04:42.403 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:04:42.404 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:04:42.405 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:04:42.405 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:04:42.406 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:04:42.406 | ab2d6fadc0144962940be7d7b7817e96 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:04:42.411 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-28 09:04:42.411 |  | INFO     | module_stream.service.task_execution_service:add_monitor_client:3184 - 客户端 44a90e49-b582-41a6-ab01-8797549a7e27 已连接到任务 12 的监控流
2025-07-28 09:04:42.412 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 44a90e49-b582-41a6-ab01-8797549a7e27 连接到任务 12 的监控流 (无认证模式)
2025-07-28 09:04:43.441 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:43.441 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 24
2025-07-28 09:04:43.441 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [447, 304, 530, 391]}
2025-07-28 09:04:43.441 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [447, 304, 530, 391]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [404, 410, 516, 555]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [225, 386, 365, 506]}]
2025-07-28 09:04:43.442 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:43.442 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:43.442 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:43.442 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:43.444 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:43.444 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:43.445 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:43.445 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 54 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:43.445 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:43.445 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [447, 304, 530, 391], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:43.445 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:43.445 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 54 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.9, 'name': 'car', 'ch_name': 'car', 'xyxy': [447, 304, 530, 391], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [561, 200, 621, 251], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.74, 'name': 'car', 'ch_name': 'car', 'xyxy': [1, 453, 149, 591], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.78, 'name': 'car', 'ch_name': 'car', 'xyxy': [339, 235, 419, 312], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.67, 'name': 'car', 'ch_name': 'car', 'xyxy': [440, 166, 478, 201], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.88, 'name': 'car', 'ch_name': 'car', 'xyxy': [225, 386, 365, 506], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [550, 166, 593, 200], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [457, 204, 510, 247], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.56, 'name': 'car', 'ch_name': 'car', 'xyxy': [488, 153, 524, 184], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [416, 143, 458, 180], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [151, 301, 261, 392], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.75, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 243, 90, 306], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [533, 136, 569, 162], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1238, 226], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [80, 174, 142, 220], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 105, 513, 131], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [237, 228, 320, 301], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [605, 400, 734, 513], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 105, 560, 130], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 109, 514, 133], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [219, 150, 260, 181], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 116, 573, 162], 'track_id': 21, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [360, 528, 509, 670], 'track_id': 22, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [242, 223, 350, 298], 'track_id': 23, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [351, 558, 506, 670], 'track_id': 24, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 223, 348, 295], 'track_id': 25, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [784, 169, 890, 207], 'track_id': 26, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.27, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [408, 156, 452, 194], 'track_id': 27, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [370, 500, 509, 668], 'track_id': 28, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [56, 217, 145, 271], 'track_id': 29, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [227, 229, 320, 312], 'track_id': 30, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [778, 169, 882, 208], 'track_id': 31, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 114, 515, 137], 'track_id': 32, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 111, 515, 135], 'track_id': 33, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.67, 'name': 'car', 'ch_name': 'car', 'xyxy': [231, 228, 318, 307], 'track_id': 34, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [378, 474, 513, 663], 'track_id': 35, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [774, 171, 879, 208], 'track_id': 36, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [387, 450, 516, 621], 'track_id': 37, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 632, 180, 674], 'track_id': 38, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [433, 164, 482, 198], 'track_id': 39, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [770, 171, 871, 208], 'track_id': 40, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [204, 157, 250, 189], 'track_id': 41, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [396, 426, 516, 589], 'track_id': 42, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.75, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 600, 230, 674], 'track_id': 43, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.63, 'name': 'car', 'ch_name': 'car', 'xyxy': [236, 228, 319, 301], 'track_id': 44, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [763, 173, 864, 206], 'track_id': 45, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 107, 514, 131], 'track_id': 46, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [539, 156, 581, 201], 'track_id': 47, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [404, 410, 516, 555], 'track_id': 48, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [758, 171, 858, 206], 'track_id': 49, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.43, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [1, 573, 249, 671], 'track_id': 50, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.42, 'name': 'car', 'ch_name': 'car', 'xyxy': [190, 163, 241, 196], 'track_id': 51, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [452, 101, 476, 121], 'track_id': 52, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 573, 252, 672], 'track_id': 53, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:43.444740', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664683.4447465}
2025-07-28 09:04:44.502 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:44.502 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 22
2025-07-28 09:04:44.502 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 370, 376, 489]}
2025-07-28 09:04:44.502 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 370, 376, 489]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [603, 375, 728, 486]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [410, 383, 518, 532]}]
2025-07-28 09:04:44.503 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:44.503 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:44.503 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:44.503 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:44.505 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:44.506 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:44.506 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:44.506 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 59 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:44.506 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:44.506 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [450, 297, 530, 378], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:44.506 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:44.506 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 59 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [450, 297, 530, 378], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [559, 195, 620, 244], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 447, 153, 594], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.74, 'name': 'car', 'ch_name': 'car', 'xyxy': [343, 229, 421, 302], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [438, 162, 480, 197], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 370, 376, 489], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.65, 'name': 'car', 'ch_name': 'car', 'xyxy': [547, 164, 591, 200], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.74, 'name': 'car', 'ch_name': 'car', 'xyxy': [456, 196, 512, 246], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.55, 'name': 'car', 'ch_name': 'car', 'xyxy': [488, 152, 524, 181], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [416, 143, 458, 180], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [156, 299, 263, 388], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.58, 'name': 'car', 'ch_name': 'car', 'xyxy': [1, 253, 71, 321], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.55, 'name': 'car', 'ch_name': 'car', 'xyxy': [531, 134, 565, 159], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1238, 226], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [68, 177, 130, 225], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [533, 103, 559, 129], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [236, 228, 319, 303], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [603, 375, 728, 486], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 105, 560, 130], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 109, 514, 133], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [219, 150, 260, 181], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 116, 573, 162], 'track_id': 21, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [360, 528, 509, 670], 'track_id': 22, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [242, 223, 350, 298], 'track_id': 23, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [351, 558, 506, 670], 'track_id': 24, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 223, 348, 295], 'track_id': 25, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [784, 169, 890, 207], 'track_id': 26, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.27, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [408, 156, 452, 194], 'track_id': 27, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [370, 500, 509, 668], 'track_id': 28, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [56, 217, 145, 271], 'track_id': 29, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [227, 229, 320, 312], 'track_id': 30, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [778, 169, 882, 208], 'track_id': 31, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 114, 515, 137], 'track_id': 32, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 111, 515, 135], 'track_id': 33, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.67, 'name': 'car', 'ch_name': 'car', 'xyxy': [231, 228, 318, 307], 'track_id': 34, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [378, 474, 513, 663], 'track_id': 35, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [774, 171, 879, 208], 'track_id': 36, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [387, 450, 516, 621], 'track_id': 37, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 632, 180, 674], 'track_id': 38, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [433, 164, 482, 198], 'track_id': 39, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [770, 171, 871, 208], 'track_id': 40, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [204, 157, 250, 189], 'track_id': 41, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [396, 426, 516, 589], 'track_id': 42, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.75, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 600, 230, 674], 'track_id': 43, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.63, 'name': 'car', 'ch_name': 'car', 'xyxy': [236, 228, 319, 301], 'track_id': 44, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [763, 173, 864, 206], 'track_id': 45, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 107, 514, 131], 'track_id': 46, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [539, 156, 581, 201], 'track_id': 47, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [404, 410, 516, 555], 'track_id': 48, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [758, 171, 858, 206], 'track_id': 49, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.43, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [1, 573, 249, 671], 'track_id': 50, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.42, 'name': 'car', 'ch_name': 'car', 'xyxy': [190, 163, 241, 196], 'track_id': 51, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [452, 101, 476, 121], 'track_id': 52, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 573, 252, 672], 'track_id': 53, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [410, 383, 518, 532], 'track_id': 54, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.55, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [1, 548, 266, 674], 'track_id': 55, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [186, 166, 236, 199], 'track_id': 56, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [451, 101, 476, 121], 'track_id': 57, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.27, 'name': 'car', 'ch_name': 'car', 'xyxy': [753, 170, 851, 205], 'track_id': 58, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:44.505904', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664684.5059106}
2025-07-28 09:04:45.551 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:45.551 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 22
2025-07-28 09:04:45.551 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [246, 370, 382, 472]}
2025-07-28 09:04:45.551 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [246, 370, 382, 472]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [600, 360, 717, 468]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [158, 296, 267, 382]}]
2025-07-28 09:04:45.552 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:45.552 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:45.552 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:45.552 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:45.554 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:45.555 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:45.555 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:45.555 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 67 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:45.555 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:45.555 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [453, 289, 529, 358], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:45.555 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:45.555 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 67 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [453, 289, 529, 358], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.78, 'name': 'car', 'ch_name': 'car', 'xyxy': [559, 189, 617, 239], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 442, 155, 563], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [348, 225, 424, 293], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.42, 'name': 'car', 'ch_name': 'car', 'xyxy': [439, 161, 479, 195], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [246, 370, 382, 472], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.56, 'name': 'car', 'ch_name': 'car', 'xyxy': [548, 161, 589, 195], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [458, 192, 509, 237], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [488, 149, 524, 180], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [416, 143, 458, 180], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [158, 296, 267, 382], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.58, 'name': 'car', 'ch_name': 'car', 'xyxy': [1, 253, 71, 321], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [533, 102, 557, 124], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1238, 226], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [68, 177, 130, 225], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [533, 103, 559, 129], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [234, 228, 319, 305], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [600, 360, 717, 468], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 105, 560, 130], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 109, 514, 133], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [219, 150, 260, 181], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 116, 573, 162], 'track_id': 21, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [360, 528, 509, 670], 'track_id': 22, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [242, 223, 350, 298], 'track_id': 23, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [351, 558, 506, 670], 'track_id': 24, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 223, 348, 295], 'track_id': 25, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [784, 169, 890, 207], 'track_id': 26, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.27, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [408, 156, 452, 194], 'track_id': 27, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [370, 500, 509, 668], 'track_id': 28, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [56, 217, 145, 271], 'track_id': 29, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [227, 229, 320, 312], 'track_id': 30, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [778, 169, 882, 208], 'track_id': 31, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 114, 515, 137], 'track_id': 32, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 111, 515, 135], 'track_id': 33, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.67, 'name': 'car', 'ch_name': 'car', 'xyxy': [231, 228, 318, 307], 'track_id': 34, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [378, 474, 513, 663], 'track_id': 35, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [774, 171, 879, 208], 'track_id': 36, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [387, 450, 516, 621], 'track_id': 37, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 632, 180, 674], 'track_id': 38, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [433, 164, 482, 198], 'track_id': 39, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [770, 171, 871, 208], 'track_id': 40, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [204, 157, 250, 189], 'track_id': 41, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [396, 426, 516, 589], 'track_id': 42, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.75, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 600, 230, 674], 'track_id': 43, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.63, 'name': 'car', 'ch_name': 'car', 'xyxy': [236, 228, 319, 301], 'track_id': 44, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [763, 173, 864, 206], 'track_id': 45, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 107, 514, 131], 'track_id': 46, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [539, 156, 581, 201], 'track_id': 47, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [404, 410, 516, 555], 'track_id': 48, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [758, 171, 858, 206], 'track_id': 49, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.43, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [1, 573, 249, 671], 'track_id': 50, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.42, 'name': 'car', 'ch_name': 'car', 'xyxy': [190, 163, 241, 196], 'track_id': 51, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [452, 101, 476, 121], 'track_id': 52, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 573, 252, 672], 'track_id': 53, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [410, 383, 518, 532], 'track_id': 54, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.55, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [1, 548, 266, 674], 'track_id': 55, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [186, 166, 236, 199], 'track_id': 56, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [451, 101, 476, 121], 'track_id': 57, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.27, 'name': 'car', 'ch_name': 'car', 'xyxy': [753, 170, 851, 205], 'track_id': 58, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [418, 367, 519, 511], 'track_id': 59, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [58, 180, 124, 229], 'track_id': 60, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.59, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [4, 523, 271, 671], 'track_id': 61, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [177, 168, 231, 203], 'track_id': 62, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [530, 134, 561, 157], 'track_id': 63, 'color': [0, 255, 0]}, {'label': 5, 'conf': 0.42, 'name': 'bus', 'ch_name': 'bus', 'xyxy': [1, 523, 276, 674], 'track_id': 64, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [750, 169, 838, 205], 'track_id': 65, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.26, 'name': 'car', 'ch_name': 'car', 'xyxy': [871, 175, 897, 198], 'track_id': 66, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:45.554938', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664685.5549445}
2025-07-28 09:04:46.717 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:46.717 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 21
2025-07-28 09:04:46.717 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [261, 355, 384, 452]}
2025-07-28 09:04:46.717 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [261, 355, 384, 452]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [602, 352, 704, 447]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [559, 187, 613, 233]}]
2025-07-28 09:04:46.718 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:46.718 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:46.718 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:46.718 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:46.720 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:46.720 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:46.720 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:46.720 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 74 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:46.721 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:46.721 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.74, 'name': 'car', 'ch_name': 'car', 'xyxy': [455, 274, 537, 354], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:46.721 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:46.721 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 74 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.74, 'name': 'car', 'ch_name': 'car', 'xyxy': [455, 274, 537, 354], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [559, 187, 613, 233], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.35, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 440, 159, 558], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.74, 'name': 'car', 'ch_name': 'car', 'xyxy': [355, 220, 427, 288], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [440, 158, 475, 188], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.86, 'name': 'car', 'ch_name': 'car', 'xyxy': [261, 355, 384, 452], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [548, 159, 589, 189], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [460, 189, 509, 229], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [488, 149, 524, 178], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [416, 143, 458, 180], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [159, 297, 268, 379], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.58, 'name': 'car', 'ch_name': 'car', 'xyxy': [1, 253, 71, 321], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.26, 'name': 'car', 'ch_name': 'car', 'xyxy': [531, 101, 557, 124], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1238, 226], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [68, 177, 130, 225], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [533, 103, 559, 129], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.67, 'name': 'car', 'ch_name': 'car', 'xyxy': [237, 228, 320, 302], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [602, 352, 704, 447], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 105, 560, 130], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 109, 514, 133], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [219, 150, 260, 181], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 116, 573, 162], 'track_id': 21, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [360, 528, 509, 670], 'track_id': 22, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [242, 223, 350, 298], 'track_id': 23, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [351, 558, 506, 670], 'track_id': 24, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 223, 348, 295], 'track_id': 25, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [784, 169, 890, 207], 'track_id': 26, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.27, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [408, 156, 452, 194], 'track_id': 27, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [370, 500, 509, 668], 'track_id': 28, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [56, 217, 145, 271], 'track_id': 29, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [227, 229, 320, 312], 'track_id': 30, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [778, 169, 882, 208], 'track_id': 31, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 114, 515, 137], 'track_id': 32, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 111, 515, 135], 'track_id': 33, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.67, 'name': 'car', 'ch_name': 'car', 'xyxy': [231, 228, 318, 307], 'track_id': 34, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [378, 474, 513, 663], 'track_id': 35, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [774, 171, 879, 208], 'track_id': 36, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [387, 450, 516, 621], 'track_id': 37, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 632, 180, 674], 'track_id': 38, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [433, 164, 482, 198], 'track_id': 39, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [770, 171, 871, 208], 'track_id': 40, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [204, 157, 250, 189], 'track_id': 41, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [396, 426, 516, 589], 'track_id': 42, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.75, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 600, 230, 674], 'track_id': 43, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.63, 'name': 'car', 'ch_name': 'car', 'xyxy': [236, 228, 319, 301], 'track_id': 44, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [763, 173, 864, 206], 'track_id': 45, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 107, 514, 131], 'track_id': 46, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [539, 156, 581, 201], 'track_id': 47, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [404, 410, 516, 555], 'track_id': 48, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [758, 171, 858, 206], 'track_id': 49, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.43, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [1, 573, 249, 671], 'track_id': 50, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.42, 'name': 'car', 'ch_name': 'car', 'xyxy': [190, 163, 241, 196], 'track_id': 51, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [452, 101, 476, 121], 'track_id': 52, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 573, 252, 672], 'track_id': 53, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [410, 383, 518, 532], 'track_id': 54, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.55, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [1, 548, 266, 674], 'track_id': 55, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [186, 166, 236, 199], 'track_id': 56, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [451, 101, 476, 121], 'track_id': 57, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.27, 'name': 'car', 'ch_name': 'car', 'xyxy': [753, 170, 851, 205], 'track_id': 58, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [418, 367, 519, 511], 'track_id': 59, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [58, 180, 124, 229], 'track_id': 60, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.59, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [4, 523, 271, 671], 'track_id': 61, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [177, 168, 231, 203], 'track_id': 62, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [530, 134, 561, 157], 'track_id': 63, 'color': [0, 255, 0]}, {'label': 5, 'conf': 0.42, 'name': 'bus', 'ch_name': 'bus', 'xyxy': [1, 523, 276, 674], 'track_id': 64, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [750, 169, 838, 205], 'track_id': 65, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.26, 'name': 'car', 'ch_name': 'car', 'xyxy': [871, 175, 897, 198], 'track_id': 66, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [422, 364, 521, 481], 'track_id': 67, 'color': [0, 255, 0]}, {'label': 5, 'conf': 0.68, 'name': 'bus', 'ch_name': 'bus', 'xyxy': [27, 500, 284, 674], 'track_id': 68, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [47, 183, 117, 234], 'track_id': 69, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.48, 'name': 'car', 'ch_name': 'car', 'xyxy': [170, 171, 224, 209], 'track_id': 70, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [527, 128, 557, 153], 'track_id': 71, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [746, 168, 836, 204], 'track_id': 72, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [673, 637, 1008, 674], 'track_id': 73, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:46.720706', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664686.7207115}
2025-07-28 09:04:47.739 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:47.739 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 20
2025-07-28 09:04:47.740 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [457, 269, 531, 343]}
2025-07-28 09:04:47.740 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [457, 269, 531, 343]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [595, 340, 696, 433]}, {'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [272, 338, 387, 444]}]
2025-07-28 09:04:47.740 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:47.740 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:47.740 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:47.740 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:47.743 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:47.743 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:47.743 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:47.743 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 83 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:47.743 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:47.743 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [457, 269, 531, 343], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:47.744 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:47.744 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 83 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [457, 269, 531, 343], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.66, 'name': 'car', 'ch_name': 'car', 'xyxy': [552, 183, 611, 228], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.64, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 434, 163, 554], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [358, 212, 431, 285], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.29, 'name': 'car', 'ch_name': 'car', 'xyxy': [438, 151, 473, 185], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [272, 338, 387, 444], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.48, 'name': 'car', 'ch_name': 'car', 'xyxy': [546, 153, 587, 185], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [461, 184, 510, 224], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 147, 523, 175], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [416, 143, 458, 180], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.78, 'name': 'car', 'ch_name': 'car', 'xyxy': [162, 296, 269, 379], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.58, 'name': 'car', 'ch_name': 'car', 'xyxy': [1, 253, 71, 321], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.26, 'name': 'car', 'ch_name': 'car', 'xyxy': [531, 101, 557, 124], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1239, 227], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [68, 177, 130, 225], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [533, 103, 559, 129], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.67, 'name': 'car', 'ch_name': 'car', 'xyxy': [237, 228, 320, 302], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [602, 352, 704, 447], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 105, 560, 130], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 109, 514, 133], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [219, 150, 260, 181], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 116, 573, 162], 'track_id': 21, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [360, 528, 509, 670], 'track_id': 22, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [242, 223, 350, 298], 'track_id': 23, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [351, 558, 506, 670], 'track_id': 24, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 223, 348, 295], 'track_id': 25, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [784, 169, 890, 207], 'track_id': 26, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.27, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [408, 156, 452, 194], 'track_id': 27, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [370, 500, 509, 668], 'track_id': 28, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [56, 217, 145, 271], 'track_id': 29, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [227, 229, 320, 312], 'track_id': 30, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [778, 169, 882, 208], 'track_id': 31, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 114, 515, 137], 'track_id': 32, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 111, 515, 135], 'track_id': 33, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.67, 'name': 'car', 'ch_name': 'car', 'xyxy': [231, 228, 318, 307], 'track_id': 34, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [378, 474, 513, 663], 'track_id': 35, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [774, 171, 879, 208], 'track_id': 36, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [387, 450, 516, 621], 'track_id': 37, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 632, 180, 674], 'track_id': 38, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [433, 164, 482, 198], 'track_id': 39, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [770, 171, 871, 208], 'track_id': 40, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [204, 157, 250, 189], 'track_id': 41, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [396, 426, 516, 589], 'track_id': 42, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.75, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 600, 230, 674], 'track_id': 43, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.63, 'name': 'car', 'ch_name': 'car', 'xyxy': [236, 228, 319, 301], 'track_id': 44, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [763, 173, 864, 206], 'track_id': 45, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 107, 514, 131], 'track_id': 46, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [539, 156, 581, 201], 'track_id': 47, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [404, 410, 516, 555], 'track_id': 48, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [758, 171, 858, 206], 'track_id': 49, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.43, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [1, 573, 249, 671], 'track_id': 50, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.42, 'name': 'car', 'ch_name': 'car', 'xyxy': [190, 163, 241, 196], 'track_id': 51, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [452, 101, 476, 121], 'track_id': 52, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 573, 252, 672], 'track_id': 53, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [410, 383, 518, 532], 'track_id': 54, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.55, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [1, 548, 266, 674], 'track_id': 55, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [186, 166, 236, 199], 'track_id': 56, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [451, 101, 476, 121], 'track_id': 57, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.27, 'name': 'car', 'ch_name': 'car', 'xyxy': [753, 170, 851, 205], 'track_id': 58, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [418, 367, 519, 511], 'track_id': 59, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [58, 180, 124, 229], 'track_id': 60, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.59, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [4, 523, 271, 671], 'track_id': 61, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [177, 168, 231, 203], 'track_id': 62, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [530, 134, 561, 157], 'track_id': 63, 'color': [0, 255, 0]}, {'label': 5, 'conf': 0.42, 'name': 'bus', 'ch_name': 'bus', 'xyxy': [1, 523, 276, 674], 'track_id': 64, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [750, 169, 838, 205], 'track_id': 65, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.26, 'name': 'car', 'ch_name': 'car', 'xyxy': [871, 175, 897, 198], 'track_id': 66, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [422, 364, 521, 481], 'track_id': 67, 'color': [0, 255, 0]}, {'label': 5, 'conf': 0.68, 'name': 'bus', 'ch_name': 'bus', 'xyxy': [27, 500, 284, 674], 'track_id': 68, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [47, 183, 117, 234], 'track_id': 69, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.48, 'name': 'car', 'ch_name': 'car', 'xyxy': [170, 171, 224, 209], 'track_id': 70, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [527, 128, 557, 153], 'track_id': 71, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [746, 168, 836, 204], 'track_id': 72, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [673, 637, 1008, 674], 'track_id': 73, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [595, 340, 696, 433], 'track_id': 74, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [428, 342, 520, 456], 'track_id': 75, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 231, 319, 296], 'track_id': 76, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [740, 169, 834, 204], 'track_id': 77, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.5, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [49, 478, 296, 673], 'track_id': 78, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [525, 125, 556, 151], 'track_id': 79, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [32, 185, 109, 242], 'track_id': 80, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [661, 581, 1006, 673], 'track_id': 81, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.34, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [661, 585, 1002, 674], 'track_id': 82, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:47.743014', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664687.743021}
2025-07-28 09:04:48.798 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:48.799 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 22
2025-07-28 09:04:48.799 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [286, 324, 401, 421]}
2025-07-28 09:04:48.799 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [286, 324, 401, 421]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [435, 321, 524, 435]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [461, 258, 530, 325]}]
2025-07-28 09:04:48.799 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:48.799 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:48.799 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:48.800 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:48.802 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:48.802 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:48.802 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: False
2025-07-28 09:04:48.802 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 检测到 94 个车辆目标，计数: 0 (阈值: 1)
2025-07-28 09:04:48.803 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:48.803 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [461, 258, 530, 325], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:48.803 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:48.803 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': False, 'message': '检测到 94 个车辆目标，计数: 0 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [461, 258, 530, 325], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.63, 'name': 'car', 'ch_name': 'car', 'xyxy': [553, 172, 606, 222], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.69, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 425, 170, 544], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [362, 207, 432, 267], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.72, 'name': 'car', 'ch_name': 'car', 'xyxy': [463, 177, 510, 217], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.83, 'name': 'car', 'ch_name': 'car', 'xyxy': [286, 324, 401, 421], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [545, 152, 584, 180], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [461, 184, 510, 224], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.43, 'name': 'car', 'ch_name': 'car', 'xyxy': [524, 121, 554, 150], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [416, 143, 458, 180], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [165, 295, 270, 376], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.58, 'name': 'car', 'ch_name': 'car', 'xyxy': [1, 253, 71, 321], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.26, 'name': 'car', 'ch_name': 'car', 'xyxy': [531, 101, 557, 124], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.57, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1239, 227], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [68, 177, 130, 225], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [533, 103, 559, 129], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [200, 262, 325, 348], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [602, 352, 704, 447], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 105, 560, 130], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 109, 514, 133], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [219, 150, 260, 181], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 116, 573, 162], 'track_id': 21, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [360, 528, 509, 670], 'track_id': 22, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [242, 223, 350, 298], 'track_id': 23, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [351, 558, 506, 670], 'track_id': 24, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 223, 348, 295], 'track_id': 25, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [784, 169, 890, 207], 'track_id': 26, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.27, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [408, 156, 452, 194], 'track_id': 27, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [370, 500, 509, 668], 'track_id': 28, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [56, 217, 145, 271], 'track_id': 29, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [227, 229, 320, 312], 'track_id': 30, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [778, 169, 882, 208], 'track_id': 31, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 114, 515, 137], 'track_id': 32, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 111, 515, 135], 'track_id': 33, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.67, 'name': 'car', 'ch_name': 'car', 'xyxy': [231, 228, 318, 307], 'track_id': 34, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [378, 474, 513, 663], 'track_id': 35, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [774, 171, 879, 208], 'track_id': 36, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [387, 450, 516, 621], 'track_id': 37, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 632, 180, 674], 'track_id': 38, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [433, 164, 482, 198], 'track_id': 39, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [770, 171, 871, 208], 'track_id': 40, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [204, 157, 250, 189], 'track_id': 41, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [396, 426, 516, 589], 'track_id': 42, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.75, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 600, 230, 674], 'track_id': 43, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.63, 'name': 'car', 'ch_name': 'car', 'xyxy': [236, 228, 319, 301], 'track_id': 44, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [763, 173, 864, 206], 'track_id': 45, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 107, 514, 131], 'track_id': 46, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [539, 156, 581, 201], 'track_id': 47, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [404, 410, 516, 555], 'track_id': 48, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [758, 171, 858, 206], 'track_id': 49, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.43, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [1, 573, 249, 671], 'track_id': 50, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.42, 'name': 'car', 'ch_name': 'car', 'xyxy': [190, 163, 241, 196], 'track_id': 51, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [452, 101, 476, 121], 'track_id': 52, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 573, 252, 672], 'track_id': 53, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [410, 383, 518, 532], 'track_id': 54, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.55, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [1, 548, 266, 674], 'track_id': 55, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [186, 166, 236, 199], 'track_id': 56, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [451, 101, 476, 121], 'track_id': 57, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.27, 'name': 'car', 'ch_name': 'car', 'xyxy': [753, 170, 851, 205], 'track_id': 58, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [418, 367, 519, 511], 'track_id': 59, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [58, 180, 124, 229], 'track_id': 60, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.59, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [4, 523, 271, 671], 'track_id': 61, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [177, 168, 231, 203], 'track_id': 62, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [530, 134, 561, 157], 'track_id': 63, 'color': [0, 255, 0]}, {'label': 5, 'conf': 0.42, 'name': 'bus', 'ch_name': 'bus', 'xyxy': [1, 523, 276, 674], 'track_id': 64, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [750, 169, 838, 205], 'track_id': 65, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.26, 'name': 'car', 'ch_name': 'car', 'xyxy': [871, 175, 897, 198], 'track_id': 66, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [422, 364, 521, 481], 'track_id': 67, 'color': [0, 255, 0]}, {'label': 5, 'conf': 0.68, 'name': 'bus', 'ch_name': 'bus', 'xyxy': [27, 500, 284, 674], 'track_id': 68, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [47, 183, 117, 234], 'track_id': 69, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.48, 'name': 'car', 'ch_name': 'car', 'xyxy': [170, 171, 224, 209], 'track_id': 70, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [527, 128, 557, 153], 'track_id': 71, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [746, 168, 836, 204], 'track_id': 72, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [673, 637, 1008, 674], 'track_id': 73, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [595, 340, 696, 433], 'track_id': 74, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [428, 342, 520, 456], 'track_id': 75, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 231, 319, 296], 'track_id': 76, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [740, 169, 834, 204], 'track_id': 77, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.5, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [49, 478, 296, 673], 'track_id': 78, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [525, 125, 556, 151], 'track_id': 79, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [32, 185, 109, 242], 'track_id': 80, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [661, 581, 1006, 673], 'track_id': 81, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.34, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [661, 585, 1002, 674], 'track_id': 82, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [435, 321, 524, 435], 'track_id': 83, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [592, 316, 688, 404], 'track_id': 84, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [246, 229, 320, 293], 'track_id': 85, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.62, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [89, 449, 313, 663], 'track_id': 86, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.58, 'name': 'car', 'ch_name': 'car', 'xyxy': [647, 514, 990, 669], 'track_id': 87, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [19, 189, 92, 249], 'track_id': 88, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [352, 641, 524, 674], 'track_id': 89, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.42, 'name': 'car', 'ch_name': 'car', 'xyxy': [729, 166, 827, 200], 'track_id': 90, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 142, 522, 170], 'track_id': 91, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.36, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [639, 517, 1000, 670], 'track_id': 92, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [149, 176, 207, 220], 'track_id': 93, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 0, 'total_line_count': 0, 'total_alert_count': 0, 'alert_threshold': 1, 'alert_messages': [], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:48.802264', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664688.8022702}
2025-07-28 09:04:49.846 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:846 - 🔍 任务12 - 检测结果类型: <class 'list'>
2025-07-28 09:04:49.846 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:848 - 🔍 任务12 - 检测结果数量: 24
2025-07-28 09:04:49.846 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:850 - 🔍 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [592, 304, 684, 389]}
2025-07-28 09:04:49.846 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 🔍 任务12 - 前3个检测结果: [{'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [592, 304, 684, 389]}, {'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [293, 309, 402, 405]}, {'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [462, 248, 530, 317]}]
2025-07-28 09:04:49.846 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:915 - 任务配置 - 检测区域: 1个, 多边形: 0个
2025-07-28 09:04:49.846 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:916 - 任务配置 - 检测线段: 0个, 计数线: 0个
2025-07-28 09:04:49.846 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:918 - 第一个检测区域: {'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}
2025-07-28 09:04:49.847 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:951 - 车辆计数配置: 计数线0条, 检测区域1个
2025-07-28 09:04:49.849 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:956 - ⚠️ 任务12 - 后处理结果类型: <class 'dict'>
2025-07-28 09:04:49.849 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:958 - ⚠️ 任务12 - 后处理结果键: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:49.849 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:960 - ⚠️ 任务12 - hit: True
2025-07-28 09:04:49.849 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:961 - ⚠️ 任务12 - message: 车辆计数告警: 区域内检测到1辆车 (阈值: 1)
2025-07-28 09:04:49.849 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:964 - ⚠️ 任务12 - details键: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp']
2025-07-28 09:04:49.849 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:979 - ⚠️ 任务12 - 第一个检测结果: {'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [462, 248, 530, 317], 'track_id': 0, 'color': [0, 255, 0]}
2025-07-28 09:04:49.849 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:981 - ⚠️ 任务12 - 第一个检测track_id: 0
2025-07-28 09:04:49.850 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:detection_loop:1009 - 后处理结果: {'hit': True, 'message': '车辆计数告警: 区域内检测到1辆车 (阈值: 1)', 'details': {'detections': [{'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [462, 248, 530, 317], 'track_id': 0, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [551, 165, 602, 214], 'track_id': 1, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 415, 194, 540], 'track_id': 2, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [366, 201, 434, 257], 'track_id': 3, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [464, 175, 510, 211], 'track_id': 4, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.79, 'name': 'car', 'ch_name': 'car', 'xyxy': [293, 309, 402, 405], 'track_id': 5, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [544, 151, 587, 178], 'track_id': 6, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.73, 'name': 'car', 'ch_name': 'car', 'xyxy': [461, 184, 510, 224], 'track_id': 7, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [522, 120, 551, 147], 'track_id': 8, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [416, 143, 458, 180], 'track_id': 9, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.7, 'name': 'car', 'ch_name': 'car', 'xyxy': [167, 293, 269, 376], 'track_id': 10, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.58, 'name': 'car', 'ch_name': 'car', 'xyxy': [1, 253, 71, 321], 'track_id': 11, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [528, 98, 553, 120], 'track_id': 12, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.58, 'name': 'car', 'ch_name': 'car', 'xyxy': [1153, 202, 1239, 227], 'track_id': 13, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [68, 177, 130, 225], 'track_id': 14, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [525, 105, 554, 137], 'track_id': 15, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.25, 'name': 'car', 'ch_name': 'car', 'xyxy': [200, 262, 325, 348], 'track_id': 16, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.82, 'name': 'car', 'ch_name': 'car', 'xyxy': [602, 352, 704, 447], 'track_id': 17, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 105, 560, 130], 'track_id': 18, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 109, 514, 133], 'track_id': 19, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [219, 150, 260, 181], 'track_id': 20, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.34, 'name': 'car', 'ch_name': 'car', 'xyxy': [536, 116, 573, 162], 'track_id': 21, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [346, 603, 534, 671], 'track_id': 22, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [242, 223, 350, 298], 'track_id': 23, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [351, 558, 506, 670], 'track_id': 24, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 223, 348, 295], 'track_id': 25, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [784, 169, 890, 207], 'track_id': 26, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.27, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [408, 156, 452, 194], 'track_id': 27, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [370, 500, 509, 668], 'track_id': 28, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.77, 'name': 'car', 'ch_name': 'car', 'xyxy': [56, 217, 145, 271], 'track_id': 29, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [227, 229, 320, 312], 'track_id': 30, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [778, 169, 882, 208], 'track_id': 31, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 114, 515, 137], 'track_id': 32, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 111, 515, 135], 'track_id': 33, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.67, 'name': 'car', 'ch_name': 'car', 'xyxy': [231, 228, 318, 307], 'track_id': 34, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [378, 474, 513, 663], 'track_id': 35, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.47, 'name': 'car', 'ch_name': 'car', 'xyxy': [774, 171, 879, 208], 'track_id': 36, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [387, 450, 516, 621], 'track_id': 37, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 632, 180, 674], 'track_id': 38, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [433, 164, 482, 198], 'track_id': 39, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [770, 171, 871, 208], 'track_id': 40, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [204, 157, 250, 189], 'track_id': 41, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [396, 426, 516, 589], 'track_id': 42, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.75, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 600, 230, 674], 'track_id': 43, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.63, 'name': 'car', 'ch_name': 'car', 'xyxy': [236, 228, 319, 301], 'track_id': 44, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [763, 173, 864, 206], 'track_id': 45, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 107, 514, 131], 'track_id': 46, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [539, 156, 581, 201], 'track_id': 47, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.89, 'name': 'car', 'ch_name': 'car', 'xyxy': [404, 410, 516, 555], 'track_id': 48, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [758, 171, 858, 206], 'track_id': 49, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.43, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [1, 573, 249, 671], 'track_id': 50, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.42, 'name': 'car', 'ch_name': 'car', 'xyxy': [190, 163, 241, 196], 'track_id': 51, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.37, 'name': 'car', 'ch_name': 'car', 'xyxy': [452, 101, 476, 121], 'track_id': 52, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [0, 573, 252, 672], 'track_id': 53, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.87, 'name': 'car', 'ch_name': 'car', 'xyxy': [410, 383, 518, 532], 'track_id': 54, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.55, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [1, 548, 266, 674], 'track_id': 55, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.41, 'name': 'car', 'ch_name': 'car', 'xyxy': [186, 166, 236, 199], 'track_id': 56, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.33, 'name': 'car', 'ch_name': 'car', 'xyxy': [451, 101, 476, 121], 'track_id': 57, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.27, 'name': 'car', 'ch_name': 'car', 'xyxy': [753, 170, 851, 205], 'track_id': 58, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.71, 'name': 'car', 'ch_name': 'car', 'xyxy': [418, 367, 519, 511], 'track_id': 59, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.61, 'name': 'car', 'ch_name': 'car', 'xyxy': [58, 180, 124, 229], 'track_id': 60, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.59, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [4, 523, 271, 671], 'track_id': 61, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.54, 'name': 'car', 'ch_name': 'car', 'xyxy': [177, 168, 231, 203], 'track_id': 62, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [530, 134, 561, 157], 'track_id': 63, 'color': [0, 255, 0]}, {'label': 5, 'conf': 0.42, 'name': 'bus', 'ch_name': 'bus', 'xyxy': [1, 523, 276, 674], 'track_id': 64, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.39, 'name': 'car', 'ch_name': 'car', 'xyxy': [750, 169, 838, 205], 'track_id': 65, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.26, 'name': 'car', 'ch_name': 'car', 'xyxy': [871, 175, 897, 198], 'track_id': 66, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [422, 364, 521, 481], 'track_id': 67, 'color': [0, 255, 0]}, {'label': 5, 'conf': 0.68, 'name': 'bus', 'ch_name': 'bus', 'xyxy': [27, 500, 284, 674], 'track_id': 68, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.59, 'name': 'car', 'ch_name': 'car', 'xyxy': [47, 183, 117, 234], 'track_id': 69, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.48, 'name': 'car', 'ch_name': 'car', 'xyxy': [170, 171, 224, 209], 'track_id': 70, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [527, 128, 557, 153], 'track_id': 71, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.38, 'name': 'car', 'ch_name': 'car', 'xyxy': [746, 168, 836, 204], 'track_id': 72, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.28, 'name': 'car', 'ch_name': 'car', 'xyxy': [673, 637, 1008, 674], 'track_id': 73, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.84, 'name': 'car', 'ch_name': 'car', 'xyxy': [595, 340, 696, 433], 'track_id': 74, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [428, 342, 520, 456], 'track_id': 75, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.68, 'name': 'car', 'ch_name': 'car', 'xyxy': [240, 231, 319, 296], 'track_id': 76, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [740, 169, 834, 204], 'track_id': 77, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.5, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [49, 478, 296, 673], 'track_id': 78, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.5, 'name': 'car', 'ch_name': 'car', 'xyxy': [525, 125, 556, 151], 'track_id': 79, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.49, 'name': 'car', 'ch_name': 'car', 'xyxy': [32, 185, 109, 242], 'track_id': 80, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.36, 'name': 'car', 'ch_name': 'car', 'xyxy': [661, 581, 1006, 673], 'track_id': 81, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.34, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [661, 585, 1002, 674], 'track_id': 82, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.81, 'name': 'car', 'ch_name': 'car', 'xyxy': [435, 321, 524, 435], 'track_id': 83, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.8, 'name': 'car', 'ch_name': 'car', 'xyxy': [592, 316, 688, 404], 'track_id': 84, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.62, 'name': 'car', 'ch_name': 'car', 'xyxy': [246, 229, 320, 293], 'track_id': 85, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.62, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [89, 449, 313, 663], 'track_id': 86, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.58, 'name': 'car', 'ch_name': 'car', 'xyxy': [647, 514, 990, 669], 'track_id': 87, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.52, 'name': 'car', 'ch_name': 'car', 'xyxy': [19, 189, 92, 249], 'track_id': 88, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.45, 'name': 'car', 'ch_name': 'car', 'xyxy': [352, 641, 524, 674], 'track_id': 89, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.42, 'name': 'car', 'ch_name': 'car', 'xyxy': [729, 166, 827, 200], 'track_id': 90, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.4, 'name': 'car', 'ch_name': 'car', 'xyxy': [489, 142, 522, 170], 'track_id': 91, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.36, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [639, 517, 1000, 670], 'track_id': 92, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.3, 'name': 'car', 'ch_name': 'car', 'xyxy': [149, 176, 207, 220], 'track_id': 93, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.85, 'name': 'car', 'ch_name': 'car', 'xyxy': [592, 304, 684, 389], 'track_id': 94, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.76, 'name': 'car', 'ch_name': 'car', 'xyxy': [438, 312, 522, 408], 'track_id': 95, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.67, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [112, 431, 325, 645], 'track_id': 96, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.64, 'name': 'car', 'ch_name': 'car', 'xyxy': [245, 228, 322, 289], 'track_id': 97, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.55, 'name': 'car', 'ch_name': 'car', 'xyxy': [724, 166, 819, 200], 'track_id': 98, 'color': [255, 0, 0], 'alert_reason': '车辆进入区域1'}, {'label': 2, 'conf': 0.51, 'name': 'car', 'ch_name': 'car', 'xyxy': [631, 477, 988, 670], 'track_id': 99, 'color': [0, 255, 0]}, {'label': 5, 'conf': 0.46, 'name': 'bus', 'ch_name': 'bus', 'xyxy': [626, 477, 997, 670], 'track_id': 100, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [490, 142, 519, 169], 'track_id': 101, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.32, 'name': 'car', 'ch_name': 'car', 'xyxy': [146, 181, 200, 226], 'track_id': 102, 'color': [0, 255, 0]}, {'label': 2, 'conf': 0.26, 'name': 'car', 'ch_name': 'car', 'xyxy': [440, 147, 475, 176], 'track_id': 103, 'color': [0, 255, 0]}, {'label': 7, 'conf': 0.25, 'name': 'truck', 'ch_name': 'truck', 'xyxy': [6, 177, 82, 257], 'track_id': 104, 'color': [0, 255, 0]}], 'lines': {}, 'polygons': {'area_1753423276239_0': {'polygon': [[630, 182], [651, 304], [694, 354], [830, 349], [798, 252], [770, 175], [741, 162], [687, 166], [658, 171], [637, 171]], 'name': '区域1'}}, 'area_detection_count': 1, 'total_line_count': 0, 'total_alert_count': 1, 'alert_threshold': 1, 'alert_messages': ['区域内检测到1辆车'], 'frame_shape': [640, 480], 'timestamp': '2025-07-28T09:04:49.849415', 'configured_areas': [{'id': 'area_1753423276239_0', 'name': '区域1', 'type': 'polygon', 'points': [{'x': 0.49252988047808766, 'y': 0.2710036574870912}, {'x': 0.5092629482071713, 'y': 0.4517265490533563}, {'x': 0.5427290836653387, 'y': 0.5257368760757315}, {'x': 0.6487051792828685, 'y': 0.5188521944922547}, {'x': 0.624003984063745, 'y': 0.3742738812392427}, {'x': 0.6016932270916334, 'y': 0.26067663511187605}, {'x': 0.5793824701195219, 'y': 0.24174376075731496}, {'x': 0.5371513944223107, 'y': 0.24690727194492257}, {'x': 0.5148406374501993, 'y': 0.25379195352839934}, {'x': 0.49810756972111553, 'y': 0.25379195352839934}]}], 'configured_lines': []}, 'timestamp': 1753664689.8494253}
2025-07-28 09:04:49.860 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_configured_areas_and_lines:2671 - 绘制区域: 区域1, 点数: 10, 坐标: [[630, 182], [651, 304], [694, 354]]...
2025-07-28 09:04:49.861 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_alert:2457 - 使用算法包提供的告警颜色: RGB[255, 0, 0] -> BGR(0, 0, 255)
2025-07-28 09:04:49.875 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:1045 - 📢 告警推送: 任务12, 消息: 车辆计数告警，有车辆经过
2025-07-28 09:04:49.887 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2149 - 告警结果结构: hit=True, message=车辆计数告警，有车辆经过
2025-07-28 09:04:49.887 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2150 - Alert result keys: ['hit', 'message', 'details', 'timestamp']
2025-07-28 09:04:49.887 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2151 - Details keys: ['detections', 'lines', 'polygons', 'area_detection_count', 'total_line_count', 'total_alert_count', 'alert_threshold', 'alert_messages', 'frame_shape', 'timestamp', 'configured_areas', 'configured_lines']
2025-07-28 09:04:49.887 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2156 - 配置区域数量: 1
2025-07-28 09:04:49.887 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2158 - 配置线段数量: 0
2025-07-28 09:04:49.896 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_configured_areas_and_lines:2671 - 绘制区域: 区域1, 点数: 10, 坐标: [[630, 182], [651, 304], [694, 354]]...
2025-07-28 09:04:49.897 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2169 - 开始处理检测结果: 总数105个, 配置区域1个
2025-07-28 09:04:49.897 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(496, 282)不在任何配置区域内
2025-07-28 09:04:49.897 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=0, xyxy=[462, 248, 530, 317]
2025-07-28 09:04:49.897 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(576, 189)不在任何配置区域内
2025-07-28 09:04:49.897 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=1, xyxy=[551, 165, 602, 214]
2025-07-28 09:04:49.897 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(97, 477)不在任何配置区域内
2025-07-28 09:04:49.897 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=2, xyxy=[0, 415, 194, 540]
2025-07-28 09:04:49.897 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(400, 229)不在任何配置区域内
2025-07-28 09:04:49.898 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=3, xyxy=[366, 201, 434, 257]
2025-07-28 09:04:49.898 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(487, 193)不在任何配置区域内
2025-07-28 09:04:49.898 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=4, xyxy=[464, 175, 510, 211]
2025-07-28 09:04:49.898 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(347, 357)不在任何配置区域内
2025-07-28 09:04:49.898 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=5, xyxy=[293, 309, 402, 405]
2025-07-28 09:04:49.898 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(565, 164)不在任何配置区域内
2025-07-28 09:04:49.898 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=6, xyxy=[544, 151, 587, 178]
2025-07-28 09:04:49.898 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(485, 204)不在任何配置区域内
2025-07-28 09:04:49.898 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=7, xyxy=[461, 184, 510, 224]
2025-07-28 09:04:49.898 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(536, 133)不在任何配置区域内
2025-07-28 09:04:49.898 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=8, xyxy=[522, 120, 551, 147]
2025-07-28 09:04:49.899 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(437, 161)不在任何配置区域内
2025-07-28 09:04:49.899 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=9, xyxy=[416, 143, 458, 180]
2025-07-28 09:04:49.899 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(218, 334)不在任何配置区域内
2025-07-28 09:04:49.899 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=10, xyxy=[167, 293, 269, 376]
2025-07-28 09:04:49.899 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(36, 287)不在任何配置区域内
2025-07-28 09:04:49.899 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=11, xyxy=[1, 253, 71, 321]
2025-07-28 09:04:49.899 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(540, 109)不在任何配置区域内
2025-07-28 09:04:49.900 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=12, xyxy=[528, 98, 553, 120]
2025-07-28 09:04:49.900 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(1196, 214)不在任何配置区域内
2025-07-28 09:04:49.900 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=13, xyxy=[1153, 202, 1239, 227]
2025-07-28 09:04:49.900 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(99, 201)不在任何配置区域内
2025-07-28 09:04:49.900 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=14, xyxy=[68, 177, 130, 225]
2025-07-28 09:04:49.900 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(539, 121)不在任何配置区域内
2025-07-28 09:04:49.900 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=15, xyxy=[525, 105, 554, 137]
2025-07-28 09:04:49.900 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(262, 305)不在任何配置区域内
2025-07-28 09:04:49.900 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=16, xyxy=[200, 262, 325, 348]
2025-07-28 09:04:49.901 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(653, 399)不在任何配置区域内
2025-07-28 09:04:49.901 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=17, xyxy=[602, 352, 704, 447]
2025-07-28 09:04:49.901 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(548, 117)不在任何配置区域内
2025-07-28 09:04:49.901 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=18, xyxy=[536, 105, 560, 130]
2025-07-28 09:04:49.901 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(501, 121)不在任何配置区域内
2025-07-28 09:04:49.901 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=19, xyxy=[489, 109, 514, 133]
2025-07-28 09:04:49.902 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(239, 165)不在任何配置区域内
2025-07-28 09:04:49.902 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=20, xyxy=[219, 150, 260, 181]
2025-07-28 09:04:49.902 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(554, 139)不在任何配置区域内
2025-07-28 09:04:49.902 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=21, xyxy=[536, 116, 573, 162]
2025-07-28 09:04:49.902 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(440, 637)不在任何配置区域内
2025-07-28 09:04:49.902 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=22, xyxy=[346, 603, 534, 671]
2025-07-28 09:04:49.902 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(296, 260)不在任何配置区域内
2025-07-28 09:04:49.902 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=23, xyxy=[242, 223, 350, 298]
2025-07-28 09:04:49.902 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(428, 614)不在任何配置区域内
2025-07-28 09:04:49.902 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=24, xyxy=[351, 558, 506, 670]
2025-07-28 09:04:49.903 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(294, 259)不在任何配置区域内
2025-07-28 09:04:49.903 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=25, xyxy=[240, 223, 348, 295]
2025-07-28 09:04:49.903 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(837, 188)不在任何配置区域内
2025-07-28 09:04:49.903 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=26, xyxy=[784, 169, 890, 207]
2025-07-28 09:04:49.903 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(430, 175)不在任何配置区域内
2025-07-28 09:04:49.903 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=27, xyxy=[408, 156, 452, 194]
2025-07-28 09:04:49.903 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(439, 584)不在任何配置区域内
2025-07-28 09:04:49.903 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=28, xyxy=[370, 500, 509, 668]
2025-07-28 09:04:49.904 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(100, 244)不在任何配置区域内
2025-07-28 09:04:49.904 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=29, xyxy=[56, 217, 145, 271]
2025-07-28 09:04:49.904 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(273, 270)不在任何配置区域内
2025-07-28 09:04:49.904 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=30, xyxy=[227, 229, 320, 312]
2025-07-28 09:04:49.904 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(830, 188)不在任何配置区域内
2025-07-28 09:04:49.904 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=31, xyxy=[778, 169, 882, 208]
2025-07-28 09:04:49.904 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(502, 125)不在任何配置区域内
2025-07-28 09:04:49.904 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=32, xyxy=[490, 114, 515, 137]
2025-07-28 09:04:49.904 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(502, 123)不在任何配置区域内
2025-07-28 09:04:49.904 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=33, xyxy=[490, 111, 515, 135]
2025-07-28 09:04:49.905 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(274, 267)不在任何配置区域内
2025-07-28 09:04:49.905 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=34, xyxy=[231, 228, 318, 307]
2025-07-28 09:04:49.905 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(445, 568)不在任何配置区域内
2025-07-28 09:04:49.905 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=35, xyxy=[378, 474, 513, 663]
2025-07-28 09:04:49.905 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(826, 189)不在任何配置区域内
2025-07-28 09:04:49.905 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=36, xyxy=[774, 171, 879, 208]
2025-07-28 09:04:49.906 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(451, 535)不在任何配置区域内
2025-07-28 09:04:49.906 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=37, xyxy=[387, 450, 516, 621]
2025-07-28 09:04:49.906 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(90, 653)不在任何配置区域内
2025-07-28 09:04:49.906 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=38, xyxy=[0, 632, 180, 674]
2025-07-28 09:04:49.906 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(457, 181)不在任何配置区域内
2025-07-28 09:04:49.907 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=39, xyxy=[433, 164, 482, 198]
2025-07-28 09:04:49.907 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(820, 189)不在任何配置区域内
2025-07-28 09:04:49.907 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=40, xyxy=[770, 171, 871, 208]
2025-07-28 09:04:49.907 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(227, 173)不在任何配置区域内
2025-07-28 09:04:49.907 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=41, xyxy=[204, 157, 250, 189]
2025-07-28 09:04:49.907 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(456, 507)不在任何配置区域内
2025-07-28 09:04:49.907 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=42, xyxy=[396, 426, 516, 589]
2025-07-28 09:04:49.907 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(115, 637)不在任何配置区域内
2025-07-28 09:04:49.908 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=43, xyxy=[0, 600, 230, 674]
2025-07-28 09:04:49.908 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(277, 264)不在任何配置区域内
2025-07-28 09:04:49.908 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=44, xyxy=[236, 228, 319, 301]
2025-07-28 09:04:49.908 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(813, 189)不在任何配置区域内
2025-07-28 09:04:49.908 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=45, xyxy=[763, 173, 864, 206]
2025-07-28 09:04:49.908 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(501, 119)不在任何配置区域内
2025-07-28 09:04:49.908 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=46, xyxy=[489, 107, 514, 131]
2025-07-28 09:04:49.908 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(560, 178)不在任何配置区域内
2025-07-28 09:04:49.909 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=47, xyxy=[539, 156, 581, 201]
2025-07-28 09:04:49.909 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(460, 482)不在任何配置区域内
2025-07-28 09:04:49.909 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=48, xyxy=[404, 410, 516, 555]
2025-07-28 09:04:49.909 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(808, 188)不在任何配置区域内
2025-07-28 09:04:49.909 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=49, xyxy=[758, 171, 858, 206]
2025-07-28 09:04:49.909 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(125, 622)不在任何配置区域内
2025-07-28 09:04:49.909 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=50, xyxy=[1, 573, 249, 671]
2025-07-28 09:04:49.910 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(215, 179)不在任何配置区域内
2025-07-28 09:04:49.910 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=51, xyxy=[190, 163, 241, 196]
2025-07-28 09:04:49.910 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(464, 111)不在任何配置区域内
2025-07-28 09:04:49.910 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=52, xyxy=[452, 101, 476, 121]
2025-07-28 09:04:49.910 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(126, 622)不在任何配置区域内
2025-07-28 09:04:49.910 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=53, xyxy=[0, 573, 252, 672]
2025-07-28 09:04:49.910 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(464, 457)不在任何配置区域内
2025-07-28 09:04:49.910 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=54, xyxy=[410, 383, 518, 532]
2025-07-28 09:04:49.911 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(133, 611)不在任何配置区域内
2025-07-28 09:04:49.911 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=55, xyxy=[1, 548, 266, 674]
2025-07-28 09:04:49.911 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(211, 182)不在任何配置区域内
2025-07-28 09:04:49.911 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=56, xyxy=[186, 166, 236, 199]
2025-07-28 09:04:49.911 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(463, 111)不在任何配置区域内
2025-07-28 09:04:49.911 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=57, xyxy=[451, 101, 476, 121]
2025-07-28 09:04:49.911 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(802, 187)不在任何配置区域内
2025-07-28 09:04:49.911 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=58, xyxy=[753, 170, 851, 205]
2025-07-28 09:04:49.912 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(468, 439)不在任何配置区域内
2025-07-28 09:04:49.912 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=59, xyxy=[418, 367, 519, 511]
2025-07-28 09:04:49.912 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(91, 204)不在任何配置区域内
2025-07-28 09:04:49.912 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=60, xyxy=[58, 180, 124, 229]
2025-07-28 09:04:49.912 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(137, 597)不在任何配置区域内
2025-07-28 09:04:49.912 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=61, xyxy=[4, 523, 271, 671]
2025-07-28 09:04:49.913 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(204, 185)不在任何配置区域内
2025-07-28 09:04:49.913 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=62, xyxy=[177, 168, 231, 203]
2025-07-28 09:04:49.913 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(545, 145)不在任何配置区域内
2025-07-28 09:04:49.913 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=63, xyxy=[530, 134, 561, 157]
2025-07-28 09:04:49.913 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(138, 598)不在任何配置区域内
2025-07-28 09:04:49.913 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=64, xyxy=[1, 523, 276, 674]
2025-07-28 09:04:49.913 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(794, 187)不在任何配置区域内
2025-07-28 09:04:49.914 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=65, xyxy=[750, 169, 838, 205]
2025-07-28 09:04:49.914 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(884, 186)不在任何配置区域内
2025-07-28 09:04:49.914 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=66, xyxy=[871, 175, 897, 198]
2025-07-28 09:04:49.914 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(471, 422)不在任何配置区域内
2025-07-28 09:04:49.914 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=67, xyxy=[422, 364, 521, 481]
2025-07-28 09:04:49.914 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(155, 587)不在任何配置区域内
2025-07-28 09:04:49.914 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=68, xyxy=[27, 500, 284, 674]
2025-07-28 09:04:49.915 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(82, 208)不在任何配置区域内
2025-07-28 09:04:49.915 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=69, xyxy=[47, 183, 117, 234]
2025-07-28 09:04:49.915 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(197, 190)不在任何配置区域内
2025-07-28 09:04:49.915 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=70, xyxy=[170, 171, 224, 209]
2025-07-28 09:04:49.915 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(542, 140)不在任何配置区域内
2025-07-28 09:04:49.915 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=71, xyxy=[527, 128, 557, 153]
2025-07-28 09:04:49.915 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(791, 186)不在任何配置区域内
2025-07-28 09:04:49.915 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=72, xyxy=[746, 168, 836, 204]
2025-07-28 09:04:49.915 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(840, 655)不在任何配置区域内
2025-07-28 09:04:49.916 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=73, xyxy=[673, 637, 1008, 674]
2025-07-28 09:04:49.916 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(645, 386)不在任何配置区域内
2025-07-28 09:04:49.916 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=74, xyxy=[595, 340, 696, 433]
2025-07-28 09:04:49.916 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(474, 399)不在任何配置区域内
2025-07-28 09:04:49.916 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=75, xyxy=[428, 342, 520, 456]
2025-07-28 09:04:49.916 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(279, 263)不在任何配置区域内
2025-07-28 09:04:49.916 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=76, xyxy=[240, 231, 319, 296]
2025-07-28 09:04:49.916 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(787, 186)不在任何配置区域内
2025-07-28 09:04:49.916 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=77, xyxy=[740, 169, 834, 204]
2025-07-28 09:04:49.917 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(172, 575)不在任何配置区域内
2025-07-28 09:04:49.917 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=78, xyxy=[49, 478, 296, 673]
2025-07-28 09:04:49.917 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(540, 138)不在任何配置区域内
2025-07-28 09:04:49.917 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=79, xyxy=[525, 125, 556, 151]
2025-07-28 09:04:49.917 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(70, 213)不在任何配置区域内
2025-07-28 09:04:49.917 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=80, xyxy=[32, 185, 109, 242]
2025-07-28 09:04:49.917 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(833, 627)不在任何配置区域内
2025-07-28 09:04:49.917 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=81, xyxy=[661, 581, 1006, 673]
2025-07-28 09:04:49.918 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(831, 629)不在任何配置区域内
2025-07-28 09:04:49.918 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=82, xyxy=[661, 585, 1002, 674]
2025-07-28 09:04:49.918 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(479, 378)不在任何配置区域内
2025-07-28 09:04:49.918 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=83, xyxy=[435, 321, 524, 435]
2025-07-28 09:04:49.918 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(640, 360)不在任何配置区域内
2025-07-28 09:04:49.918 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=84, xyxy=[592, 316, 688, 404]
2025-07-28 09:04:49.919 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(283, 261)不在任何配置区域内
2025-07-28 09:04:49.919 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=85, xyxy=[246, 229, 320, 293]
2025-07-28 09:04:49.919 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(201, 556)不在任何配置区域内
2025-07-28 09:04:49.919 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=86, xyxy=[89, 449, 313, 663]
2025-07-28 09:04:49.919 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(818, 591)不在任何配置区域内
2025-07-28 09:04:49.919 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=87, xyxy=[647, 514, 990, 669]
2025-07-28 09:04:49.919 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(55, 219)不在任何配置区域内
2025-07-28 09:04:49.920 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=88, xyxy=[19, 189, 92, 249]
2025-07-28 09:04:49.920 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(438, 657)不在任何配置区域内
2025-07-28 09:04:49.920 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=89, xyxy=[352, 641, 524, 674]
2025-07-28 09:04:49.920 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(778, 183)不在任何配置区域内
2025-07-28 09:04:49.920 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=90, xyxy=[729, 166, 827, 200]
2025-07-28 09:04:49.920 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(505, 156)不在任何配置区域内
2025-07-28 09:04:49.921 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=91, xyxy=[489, 142, 522, 170]
2025-07-28 09:04:49.921 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(819, 593)不在任何配置区域内
2025-07-28 09:04:49.921 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=92, xyxy=[639, 517, 1000, 670]
2025-07-28 09:04:49.921 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(178, 198)不在任何配置区域内
2025-07-28 09:04:49.921 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=93, xyxy=[149, 176, 207, 220]
2025-07-28 09:04:49.921 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(638, 346)不在任何配置区域内
2025-07-28 09:04:49.921 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=94, xyxy=[592, 304, 684, 389]
2025-07-28 09:04:49.922 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(480, 360)不在任何配置区域内
2025-07-28 09:04:49.922 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=95, xyxy=[438, 312, 522, 408]
2025-07-28 09:04:49.922 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(218, 538)不在任何配置区域内
2025-07-28 09:04:49.922 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=96, xyxy=[112, 431, 325, 645]
2025-07-28 09:04:49.922 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(283, 258)不在任何配置区域内
2025-07-28 09:04:49.922 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=97, xyxy=[245, 228, 322, 289]
2025-07-28 09:04:49.922 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2344 - 检测框中心点(771, 183)在区域区域1内
2025-07-28 09:04:49.923 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2203 - 绘制区域内告警目标: track_id=98, reason=车辆进入区域1
2025-07-28 09:04:49.923 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_single_detection_as_alert:2457 - 使用算法包提供的告警颜色: RGB[255, 0, 0] -> BGR(0, 0, 255)
2025-07-28 09:04:49.936 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(809, 573)不在任何配置区域内
2025-07-28 09:04:49.936 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=99, xyxy=[631, 477, 988, 670]
2025-07-28 09:04:49.936 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(811, 573)不在任何配置区域内
2025-07-28 09:04:49.936 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=100, xyxy=[626, 477, 997, 670]
2025-07-28 09:04:49.936 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(504, 155)不在任何配置区域内
2025-07-28 09:04:49.936 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=101, xyxy=[490, 142, 519, 169]
2025-07-28 09:04:49.937 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(173, 203)不在任何配置区域内
2025-07-28 09:04:49.937 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=102, xyxy=[146, 181, 200, 226]
2025-07-28 09:04:49.937 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(457, 161)不在任何配置区域内
2025-07-28 09:04:49.937 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=103, xyxy=[440, 147, 475, 176]
2025-07-28 09:04:49.937 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_is_detection_in_configured_areas:2347 - 检测框中心点(44, 217)不在任何配置区域内
2025-07-28 09:04:49.937 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2213 - 跳过区域外目标: track_id=104, xyxy=[6, 177, 82, 257]
2025-07-28 09:04:49.937 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_draw_detection_boxes:2217 - 绘制检测结果完成: 告警目标1个, 普通目标0个, 跳过104个
2025-07-28 09:04:49.938 | 96574c6e2bc34d37806b0a46983c9dfb | DEBUG    | module_stream.service.task_execution_service:_draw_detection_boxes:2218 - 绘制详情: 总检测数=105, 告警数=1, 普通数=0, 跳过数=104
2025-07-28 09:04:49.947 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_save_alert_screenshot:2065 - 告警截图保存成功: uploads\alerts\20250728\alert_12_8_20250728_090449_886.jpg
2025-07-28 09:04:49.947 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:_save_alert_screenshot:2066 - 告警截图URL: /uploads/alerts/20250728/alert_12_8_20250728_090449_886.jpg
2025-07-28 09:04:50.959 | 5175c90fcaa0451491946b44e03d76f9 | INFO     | module_stream.service.task_execution_service:stop_task:276 - 开始停止任务: 12
2025-07-28 09:04:50.960 | 5175c90fcaa0451491946b44e03d76f9 | INFO     | module_stream.service.task_execution_service:stop_task:292 - 取消异步任务: 12
2025-07-28 09:04:50.961 | 96574c6e2bc34d37806b0a46983c9dfb | INFO     | module_stream.service.task_execution_service:detection_loop:1097 - 检测任务被取消: 12
2025-07-28 09:04:50.971 | 5175c90fcaa0451491946b44e03d76f9 | INFO     | module_stream.service.task_execution_service:stop_task:300 - 已从运行任务列表移除: 12
2025-07-28 09:04:50.971 | 5175c90fcaa0451491946b44e03d76f9 | INFO     | module_stream.service.task_execution_service:stop_task:309 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-28 09:04:50.971 | 5175c90fcaa0451491946b44e03d76f9 | INFO     | module_stream.service.task_execution_service:stop_monitor_stream:3151 - 任务 12 的监控流已停止
2025-07-28 09:04:50.972 | 5175c90fcaa0451491946b44e03d76f9 | INFO     | module_stream.service.task_execution_service:stop_task:314 - 监控流停止成功: 12
2025-07-28 09:04:50.972 | 5175c90fcaa0451491946b44e03d76f9 | INFO     | module_stream.service.task_execution_service:_clear_task_cache:131 - 任务12缓存已清除
2025-07-28 09:04:50.972 | 5175c90fcaa0451491946b44e03d76f9 | INFO     | module_stream.service.task_execution_service:stop_task:323 - 任务缓存清理成功: 12
2025-07-28 09:04:50.977 | 5175c90fcaa0451491946b44e03d76f9 | INFO     | module_stream.service.task_execution_service:stop_task:332 - 任务状态更新成功: 12
2025-07-28 09:04:50.977 | 5175c90fcaa0451491946b44e03d76f9 | INFO     | module_stream.service.task_execution_service:stop_task:344 - 任务 12 停止成功，包括实时监控流
2025-07-28 09:04:50.977 | 5175c90fcaa0451491946b44e03d76f9 | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-28 09:04:51.000 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:135 - 客户端 44a90e49-b582-41a6-ab01-8797549a7e27 主动断开连接
2025-07-28 09:04:51.000 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:164 - 客户端 44a90e49-b582-41a6-ab01-8797549a7e27 连接已清理
2025-07-28 09:04:56.623 | ce1392ffd41348cf9dc95b87285c5bdb | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:68 - 获取成功
2025-07-28 09:08:55.106 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 09:08:55.107 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
