from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from datetime import datetime
from typing import Optional, Dict, Any, List
import json
import yaml
import os
from pathlib import Path

from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_stream.dao.task_dao import TaskDao
from module_stream.dao.stream_dao import StreamDao
from module_stream.entity.do.task_do import SurveillanceTask
from module_stream.entity.do.stream_do import SurveillanceStream
from module_stream.entity.vo.algorithm_config_vo import SaveAlgorithmConfigModel
from utils.log_util import logger


class AlgorithmConfigService:
    """
    算法配置服务层
    """

    # 智驱力算法目录路径
    ALGORITHM_BASE_PATH = Path(r"D:\ai-recognition\Custom-Algorithm-main\Custom-Algorithm-main\02_CustomAlgorithm\02_Demo")

    @classmethod
    async def save_algorithm_config(
        cls, 
        query_db: AsyncSession, 
        config_model: SaveAlgorithmConfigModel, 
        user_id: int
    ) -> bool:
        """
        保存算法配置到任务表
        
        :param query_db: 数据库会话
        :param config_model: 配置模型
        :param user_id: 用户ID
        :return: 保存结果
        """
        try:
            # 1. 验证视频流是否存在且属于当前用户
            stream_info = await StreamDao.get_stream_detail_by_id(query_db, config_model.stream_id)
            if not stream_info:
                raise ServiceException(message='视频流不存在')

            if stream_info.user_id != user_id:
                raise ServiceException(message='无权限访问该视频流')

            # 2. 验证配置参数
            await cls._validate_config_params(config_model.user_config)

            # 2.1 如果是智驱力配置，进行额外验证
            if isinstance(config_model.user_config, dict) and config_model.user_config.get('algorithm_type') == 'zhiquli':
                zhiquli_config = config_model.user_config.get('zhiquli_config', {})
                validation_result = cls._validate_zhiquli_config(config_model.algorithm_id, zhiquli_config)
                if not validation_result['valid']:
                    raise ServiceException(message=f'智驱力配置验证失败: {", ".join(validation_result["errors"])}')

            # 3. 检查任务名称是否已存在
            await cls._check_task_name_unique(query_db, config_model.task_name, user_id)

            # 4. 检查是否已存在相同的任务配置
            task_query = select(SurveillanceTask).where(
                and_(
                    SurveillanceTask.stream_id == config_model.stream_id,
                    SurveillanceTask.algorithm_id == config_model.algorithm_id,
                    SurveillanceTask.create_by == str(user_id)
                )
            )
            task_result = await query_db.execute(task_query)
            existing_task = task_result.scalar_one_or_none()

            current_time = datetime.now()

            # 分离不同类型的配置参数
            algorithm_config = cls._extract_algorithm_config_from_user_config(config_model.user_config)
            bbox_config = cls._extract_bbox_config_from_user_config(config_model.user_config)
            alert_config = cls._extract_alert_config_from_user_config(config_model.user_config)
            schedule_config = cls._extract_schedule_config_from_user_config(config_model.user_config)
            user_config = cls._extract_user_config_from_user_config(config_model.user_config)

            if existing_task:
                # 如果任务名称发生变化，需要检查新名称的唯一性
                if existing_task.task_name != config_model.task_name:
                    await cls._check_task_name_unique(query_db, config_model.task_name, user_id, existing_task.task_id)

                # 更新现有任务
                existing_task.task_name = config_model.task_name
                # 优先使用中文算法名称
                existing_task.algorithm_name = config_model.algorithm_name
                existing_task.algorithm_version = config_model.algorithm_version
                existing_task.user_config = user_config if user_config else None
                existing_task.algorithm_config = algorithm_config if algorithm_config else None
                existing_task.bbox_config = bbox_config if bbox_config else None
                existing_task.alert_config = alert_config if alert_config else None
                existing_task.schedule_config = schedule_config if schedule_config else None
                existing_task.update_by = str(user_id)
                existing_task.update_time = current_time
                existing_task.remark = config_model.remark

                logger.info(f"更新算法配置: task_id={existing_task.task_id}")
                logger.info(f"配置内容: {json.dumps(config_model.user_config, ensure_ascii=False)}")
            else:
                # 创建新任务
                new_task = SurveillanceTask(
                    task_name=config_model.task_name,
                    stream_id=config_model.stream_id,
                    algorithm_id=config_model.algorithm_id,
                    # 优先使用中文算法名称
                    algorithm_name=config_model.algorithm_name,
                    algorithm_version=config_model.algorithm_version,
                    algorithm_type=config_model.algorithm_id,
                    user_config=user_config if user_config else None,
                    algorithm_config=algorithm_config if algorithm_config else None,
                    bbox_config=bbox_config if bbox_config else None,
                    alert_config=alert_config if alert_config else None,
                    schedule_config=schedule_config if schedule_config else None,
                    status='0',  # 默认停止状态
                    del_flag='0',
                    run_count=0,
                    alert_count=0,
                    error_count=0,
                    create_by=str(user_id),
                    create_time=current_time,
                    update_by=str(user_id),
                    update_time=current_time,
                    remark=config_model.remark
                )

                query_db.add(new_task)
                await query_db.flush()
                logger.info(f"创建算法配置: task_id={new_task.task_id}")
                logger.info(f"配置内容: {json.dumps(config_model.user_config, ensure_ascii=False)}")

            await query_db.commit()
            return True

        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            logger.error(f"保存算法配置失败: {e}")
            raise ServiceException(message=f"保存算法配置失败: {str(e)}")

    @classmethod
    async def _validate_config_params(cls, user_config: dict) -> None:
        """
        验证配置参数
        
        :param user_config: 用户配置
        :raises ServiceException: 验证失败时抛出异常
        """
        if not user_config:
            raise ServiceException(message='配置参数不能为空')

        # 检查必要的配置字段
        basic_params = user_config.get('basicParams')
        if not basic_params:
            raise ServiceException(message='配置缺少必要字段: basicParams')

        # 验证具体的配置项
        missing_fields = []
        
        # 检查告警窗口配置
        if 'alert_window' not in basic_params:
            missing_fields.append('alert_window')
        
        # 检查区域配置
        if 'bbox' not in basic_params:
            missing_fields.append('bbox')
        
        # 检查计划配置
        if 'plan' not in basic_params:
            missing_fields.append('plan')

        if missing_fields:
            raise ServiceException(message=f'配置缺少必要字段: {", ".join(missing_fields)}')

        # 验证告警窗口配置
        alert_window = basic_params.get('alert_window', {})
        if alert_window:
            if 'type' not in alert_window:
                raise ServiceException(message='告警窗口配置缺少type字段')
            
            if alert_window.get('type') == 'interval_threshold_window':
                required_alert_fields = ['interval', 'length', 'threshold']
                missing_alert_fields = [field for field in required_alert_fields if field not in alert_window]
                if missing_alert_fields:
                    raise ServiceException(message=f'告警窗口配置缺少字段: {", ".join(missing_alert_fields)}')

        # 验证区域配置
        bbox = basic_params.get('bbox', {})
        if bbox:
            if 'polygons' not in bbox:
                raise ServiceException(message='区域配置缺少polygons字段')
            if 'lines' not in bbox:
                raise ServiceException(message='区域配置缺少lines字段')

        # 验证计划配置
        plan = basic_params.get('plan', {})
        if plan:
            # 检查是否包含一周的计划
            weekdays = ['1', '2', '3', '4', '5', '6', '7']
            missing_days = [day for day in weekdays if day not in plan]
            if missing_days:
                raise ServiceException(message=f'计划配置缺少星期: {", ".join(missing_days)}')

        logger.info("配置参数验证通过")

    @classmethod
    async def _check_task_name_unique(cls, query_db: AsyncSession, task_name: str, user_id: int, exclude_task_id: int = None) -> None:
        """
        检查任务名称是否唯一

        :param query_db: 数据库会话
        :param task_name: 任务名称
        :param user_id: 用户ID
        :param exclude_task_id: 排除的任务ID（用于更新时）
        :raises ServiceException: 任务名称已存在时抛出异常
        """
        # 检查任务名称是否已存在
        task_query = select(SurveillanceTask).where(
            and_(
                SurveillanceTask.task_name == task_name,
                SurveillanceTask.del_flag == '0'
            )
        )

        # 如果是更新操作，排除当前任务
        if exclude_task_id:
            task_query = task_query.where(SurveillanceTask.task_id != exclude_task_id)

        task_result = await query_db.execute(task_query)
        existing_task = task_result.scalar_one_or_none()

        if existing_task:
            # 生成建议的任务名称
            suggested_names = await cls._generate_suggested_task_names(query_db, task_name, user_id)
            suggestion_text = f"建议使用: {', '.join(suggested_names[:3])}" if suggested_names else ""

            raise ServiceException(
                message=f'任务名称 "{task_name}" 已存在，请使用其他名称。{suggestion_text}'
            )

    @classmethod
    async def _generate_suggested_task_names(cls, query_db: AsyncSession, base_name: str, user_id: int, max_suggestions: int = 5) -> list:
        """
        生成建议的任务名称

        :param query_db: 数据库会话
        :param base_name: 基础名称
        :param user_id: 用户ID
        :param max_suggestions: 最大建议数量
        :return: 建议的任务名称列表
        """
        suggestions = []

        # 生成带时间戳的名称
        timestamp = datetime.now().strftime('%m%d_%H%M')
        suggestions.append(f"{base_name}_{timestamp}")

        # 生成带数字后缀的名称
        for i in range(2, max_suggestions + 1):
            candidate_name = f"{base_name}_{i}"

            # 检查候选名称是否已存在
            task_query = select(SurveillanceTask).where(
                and_(
                    SurveillanceTask.task_name == candidate_name,
                    SurveillanceTask.del_flag == '0'
                )
            )
            task_result = await query_db.execute(task_query)
            existing_task = task_result.scalar_one_or_none()

            if not existing_task:
                suggestions.append(candidate_name)
                if len(suggestions) >= max_suggestions:
                    break

        return suggestions

    @classmethod
    def _extract_algorithm_config_from_user_config(cls, user_config: dict) -> dict:
        """
        从用户配置中提取算法配置参数（模型参数）

        :param user_config: 用户配置字典
        :return: 算法配置字典
        """
        # 只处理智驱力配置格式
        if isinstance(user_config, dict) and user_config.get('algorithm_type') == 'zhiquli':
            zhiquli_config = user_config.get('zhiquli_config', {})
            model_args = zhiquli_config.get('model_args', {})

            if model_args:
                logger.info(f"提取智驱力模型参数: {model_args}")
                return model_args
            else:
                logger.warning("智驱力配置中缺少模型参数(model_args)")
                return {}

        # 非智驱力配置返回空
        logger.warning("非智驱力配置格式，无法提取模型参数")
        return {}


    @classmethod
    def _extract_bbox_config_from_user_config(cls, user_config: dict) -> dict:
        """
        从用户配置中提取区域配置参数

        :param user_config: 用户配置字典
        :return: 区域配置字典
        """
        """
        从用户配置中提取区域配置参数

        :param user_config: 用户配置字典
        :return: 区域配置字典
        """
        # 只处理智驱力配置格式
        if isinstance(user_config, dict) and user_config.get('algorithm_type') == 'zhiquli':
            zhiquli_config = user_config.get('zhiquli_config', {})
            bbox_config = zhiquli_config.get('bbox', {}).copy()  # 复制一份避免修改原数据

            # 合并前端设置的检测区域
            if 'detection_areas' in user_config and user_config['detection_areas']:
                if not bbox_config:
                    bbox_config = {}
                bbox_config['polygons'] = [
                    {
                        'name': area.get('name', f'区域{i+1}'),
                        'points': area.get('points', [])
                    }
                    for i, area in enumerate(user_config['detection_areas'])
                ]
                logger.info(f"智驱力配置合并前端检测区域: {len(user_config['detection_areas'])}个")

            if bbox_config:
                logger.info(f"提取智驱力区域配置: {bbox_config}")
                return bbox_config
            else:
                logger.warning("智驱力配置中缺少区域配置(bbox)")
                return {}

        # 非智驱力配置返回空
        logger.warning("非智驱力配置格式，无法提取区域参数")
        return {}

        # 提取前端配置的排除区域
        if algorithm_needs['supports_exclusion']:
            if 'exclusion_areas' in user_config:
                bbox_config["exclusion_areas"] = user_config['exclusion_areas']
                logger.info(f"算法 {algorithm_id} 提取到的排除区域数量: {len(user_config['exclusion_areas'])}")
            else:
                bbox_config["exclusion_areas"] = []
        else:
            bbox_config["exclusion_areas"] = []
            if 'exclusion_areas' in user_config and user_config['exclusion_areas']:
                logger.warning(f"算法 {algorithm_id} 不支持排除区域，忽略前端配置的排除区域")

        # 提取前端配置的检测线段
        if algorithm_needs['supports_lines']:
            if 'detection_lines' in user_config:
                bbox_config["detection_lines"] = user_config['detection_lines']
                logger.info(f"算法 {algorithm_id} 提取到的检测线段数量: {len(user_config['detection_lines'])}")
            else:
                bbox_config["detection_lines"] = []
                logger.info(f"算法 {algorithm_id} 支持线段但未配置检测线段")
        else:
            bbox_config["detection_lines"] = []
            if 'detection_lines' in user_config and user_config['detection_lines']:
                logger.warning(f"算法 {algorithm_id} 不支持线段检测，忽略前端配置的检测线段")

        # 从basicParams中提取bbox配置（兼容旧格式）
        basic_params = user_config.get('basicParams', {})
        if 'bbox' in basic_params:
            bbox_info = basic_params['bbox']

            # 只有算法支持时才提取对应的配置
            if algorithm_needs['supports_polygons'] and 'polygons' in bbox_info:
                bbox_config["polygons"] = bbox_info['polygons']
                logger.info(f"算法 {algorithm_id} 提取到的basicParams多边形数量: {len(bbox_info['polygons'])}")

            if algorithm_needs['supports_lines'] and 'lines' in bbox_info:
                bbox_config["lines"] = bbox_info['lines']
                logger.info(f"算法 {algorithm_id} 提取到的basicParams线段数量: {len(bbox_info['lines'])}")

        # 记录算法的几何配置需求
        bbox_config["algorithm_geometry_support"] = algorithm_needs

        return bbox_config

    @classmethod
    def _get_algorithm_geometry_needs(cls, algorithm_id: str) -> dict:
        """
        根据算法ID确定几何配置需求

        :param algorithm_id: 算法ID
        :return: 几何配置需求字典
        """
        # 默认配置
        needs = {
            "supports_polygons": True,
            "supports_lines": False,
            "supports_exclusion": False
        }

        if not algorithm_id:
            return needs

        algorithm_lower = algorithm_id.lower()

        # 计数类算法主要使用线段
        if any(keyword in algorithm_lower for keyword in ['counting', 'count']):
            needs.update({
                "supports_polygons": False,
                "supports_lines": True,
                "supports_exclusion": False
            })

        # 入侵检测主要使用多边形区域
        elif 'intrusion' in algorithm_lower:
            needs.update({
                "supports_polygons": True,
                "supports_lines": False,
                "supports_exclusion": True
            })

        # 密度检测使用多边形区域
        elif any(keyword in algorithm_lower for keyword in ['density', 'crowd']):
            needs.update({
                "supports_polygons": True,
                "supports_lines": False,
                "supports_exclusion": True
            })

        # 行为检测（跌倒、吸烟等）使用多边形区域
        elif any(keyword in algorithm_lower for keyword in ['fall', 'smoking', 'phone', 'helmet', 'behavior']):
            needs.update({
                "supports_polygons": True,
                "supports_lines": False,
                "supports_exclusion": True
            })

        # 车辆检测可能需要线段和区域
        elif 'vehicle' in algorithm_lower or 'car' in algorithm_lower:
            needs.update({
                "supports_polygons": True,
                "supports_lines": True,
                "supports_exclusion": True
            })

        # 人脸识别主要使用多边形区域
        elif 'face' in algorithm_lower:
            needs.update({
                "supports_polygons": True,
                "supports_lines": False,
                "supports_exclusion": False
            })

        logger.info(f"算法 {algorithm_id} 的几何配置需求: {needs}")
        return needs

    @classmethod
    def _extract_alert_config_from_user_config(cls, user_config: dict) -> dict:
        """
        从用户配置中提取告警配置参数

        :param user_config: 用户配置字典
        :return: 告警配置字典
        """
        # 只处理智驱力配置格式
        if isinstance(user_config, dict) and user_config.get('algorithm_type') == 'zhiquli':
            zhiquli_config = user_config.get('zhiquli_config', {})
            alert_window = zhiquli_config.get('alert_window', {})

            if alert_window:
                logger.info(f"提取智驱力告警配置: {alert_window}")
                return alert_window
            else:
                logger.warning("智驱力配置中缺少告警配置(alert_window)")
                return {}

        # 非智驱力配置返回空
        logger.warning("非智驱力配置格式，无法提取告警参数")
        return {}

    @classmethod
    def _extract_algorithm_specific_alert_params(cls, algorithm_id: str, alert_params: dict, extracted_params: dict) -> None:
        """
        提取特定算法的告警参数

        :param algorithm_id: 算法ID
        :param alert_params: 前端告警参数
        :param extracted_params: 提取的参数字典
        """
        if not algorithm_id:
            return

        algorithm_lower = algorithm_id.lower()

        # 计数类算法的特殊告警参数
        if any(keyword in algorithm_lower for keyword in ['counting', 'count']):
            if 'count_alert_threshold' in alert_params:
                extracted_params["count_alert_threshold"] = alert_params['count_alert_threshold']
            if 'count_reset_alert' in alert_params:
                extracted_params["count_reset_alert"] = alert_params['count_reset_alert']

        # 密度检测的特殊告警参数
        elif any(keyword in algorithm_lower for keyword in ['density', 'crowd']):
            if 'density_level_alert' in alert_params:
                extracted_params["density_level_alert"] = alert_params['density_level_alert']
            if 'overcrowd_threshold' in alert_params:
                extracted_params["overcrowd_threshold"] = alert_params['overcrowd_threshold']

        # 行为检测的特殊告警参数
        elif any(keyword in algorithm_lower for keyword in ['fall', 'smoking', 'phone']):
            if 'behavior_duration_threshold' in alert_params:
                extracted_params["behavior_duration_threshold"] = alert_params['behavior_duration_threshold']
            if 'continuous_alert' in alert_params:
                extracted_params["continuous_alert"] = alert_params['continuous_alert']

        # 火灾检测的特殊告警参数
        elif 'fire' in algorithm_lower:
            if 'fire_confidence_threshold' in alert_params:
                extracted_params["fire_confidence_threshold"] = alert_params['fire_confidence_threshold']
            if 'emergency_alert' in alert_params:
                extracted_params["emergency_alert"] = alert_params['emergency_alert']

    @classmethod
    def _get_algorithm_alert_type(cls, algorithm_id: str) -> str:
        """
        根据算法ID确定告警类型

        :param algorithm_id: 算法ID
        :return: 告警类型
        """
        if not algorithm_id:
            return "general"

        algorithm_lower = algorithm_id.lower()

        # 紧急告警类型
        if any(keyword in algorithm_lower for keyword in ['fire', 'fall', 'intrusion']):
            return "emergency"

        # 计数告警类型
        elif any(keyword in algorithm_lower for keyword in ['counting', 'count']):
            return "counting"

        # 行为告警类型
        elif any(keyword in algorithm_lower for keyword in ['smoking', 'phone', 'helmet']):
            return "behavior"

        # 密度告警类型
        elif any(keyword in algorithm_lower for keyword in ['density', 'crowd']):
            return "density"

        # 通用告警类型
        else:
            return "general"

    @classmethod
    async def get_algorithm_config(
        cls,
        query_db: AsyncSession,
        stream_id: int,
        algorithm_id: str,
        user_id: int
    ) -> Optional[dict]:
        """
        获取算法配置
        
        :param query_db: 数据库会话
        :param stream_id: 视频流ID
        :param algorithm_id: 算法ID
        :param user_id: 用户ID
        :return: 配置数据
        """
        try:
            task_query = select(SurveillanceTask).where(
                and_(
                    SurveillanceTask.stream_id == stream_id,
                    SurveillanceTask.algorithm_id == algorithm_id,
                    SurveillanceTask.create_by == str(user_id)
                )
            )
            task_result = await query_db.execute(task_query)
            task_info = task_result.scalar_one_or_none()

            if task_info and task_info.user_config:
                return task_info.user_config
            
            return None

        except Exception as e:
            logger.error(f"获取算法配置失败: {e}")
            return None

    @classmethod
    def get_algorithm_metadata_from_yaml(cls, algorithm_id: str, platform: str = "KS968") -> Optional[Dict[str, Any]]:
        """
        从智驱力目录获取算法元数据（从postprocessor.yaml）

        :param algorithm_id: 算法ID
        :param platform: 平台类型，默认KS968
        :return: 算法元数据
        """
        try:
            # 构建算法元数据文件路径
            metadata_path = cls.ALGORITHM_BASE_PATH / platform / algorithm_id / "postprocessor" / "postprocessor.yaml"

            if not metadata_path.exists():
                logger.warning(f"算法元数据文件不存在: {metadata_path}")
                return None

            # 读取元数据文件
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = yaml.safe_load(f)

            logger.info(f"成功读取算法元数据: {algorithm_id}")
            return metadata

        except Exception as e:
            logger.error(f"读取算法元数据失败: {algorithm_id}, 错误: {e}")
            return None

    @classmethod
    def get_algorithm_config_from_zhiquli(cls, algorithm_id: str, platform: str = "KS968") -> Optional[Dict[str, Any]]:
        """
        从智驱力目录获取算法配置信息

        :param algorithm_id: 算法ID
        :param platform: 平台类型，默认KS968
        :return: 算法配置信息
        """
        try:
            # 构建算法配置文件路径
            config_path = cls.ALGORITHM_BASE_PATH / platform / algorithm_id / "postprocessor" / f"{algorithm_id}.json"

            if not config_path.exists():
                logger.warning(f"算法配置文件不存在: {config_path}")
                return None

            # 读取配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            logger.info(f"成功读取算法配置: {algorithm_id}")
            return config_data

        except Exception as e:
            logger.error(f"读取算法配置失败: {algorithm_id}, 错误: {e}")
            return None

    @classmethod
    def get_algorithm_full_info(cls, algorithm_id: str, platform: str = "KS968") -> Optional[Dict[str, Any]]:
        """
        获取算法的完整信息，包括名称和版本

        :param algorithm_id: 算法ID
        :param platform: 平台名称
        :return: 算法完整信息
        """
        try:
            # 优先从YAML文件获取信息
            metadata = cls.get_algorithm_metadata_from_yaml(algorithm_id, platform)
            if metadata:
                return {
                    "algorithm_id": algorithm_id,
                    "algorithm_name": metadata.get("ch_name", algorithm_id.replace("_", " ").title()),
                    "version": metadata.get("version", "1.0.0"),
                    "description": metadata.get("desc", ""),
                    "source": "yaml"
                }

            # 如果YAML不存在，尝试从JSON获取
            config_data = cls.get_algorithm_config_from_zhiquli(algorithm_id, platform)
            if config_data:
                basic_params = config_data.get("basicParams", {})
                reserved_args = basic_params.get("reserved_args", {})
                ch_name = reserved_args.get("ch_name", algorithm_id.replace("_", " ").title())

                return {
                    "algorithm_id": algorithm_id,
                    "algorithm_name": ch_name,
                    "version": "1.0.0",  # JSON中通常没有版本信息
                    "description": f"{ch_name} 算法",
                    "source": "json"
                }

            # 如果都没有，返回默认信息
            return {
                "algorithm_id": algorithm_id,
                "algorithm_name": algorithm_id.replace("_", " ").title(),
                "version": "1.0.0",
                "description": f"{algorithm_id} 算法",
                "source": "default"
            }

        except Exception as e:
            logger.error(f"获取算法完整信息失败: {algorithm_id}, 错误: {e}")
            return None

    @classmethod
    def get_algorithm_render_config(cls, algorithm_id: str, platform: str = "KS968") -> Optional[Dict[str, Any]]:
        """
        获取算法前端渲染配置

        :param algorithm_id: 算法ID
        :param platform: 平台类型，默认KS968
        :return: 前端渲染配置
        """
        config_data = cls.get_algorithm_config_from_zhiquli(algorithm_id, platform)
        if not config_data:
            return None

        return config_data.get('renderParams', {})

    @classmethod
    def get_algorithm_default_params(cls, algorithm_id: str, platform: str = "KS968") -> Optional[Dict[str, Any]]:
        """
        获取算法默认参数

        :param algorithm_id: 算法ID
        :param platform: 平台类型，默认KS968
        :return: 默认参数
        """
        config_data = cls.get_algorithm_config_from_zhiquli(algorithm_id, platform)
        if not config_data:
            return None

        return config_data.get('basicParams', {})

    @classmethod
    def list_available_algorithms(cls, platform: str = "KS968") -> List[str]:
        """
        列出可用的算法

        :param platform: 平台类型，默认KS968
        :return: 算法ID列表
        """
        try:
            platform_path = cls.ALGORITHM_BASE_PATH / platform
            if not platform_path.exists():
                logger.warning(f"平台目录不存在: {platform_path}")
                return []

            algorithms = []
            for item in platform_path.iterdir():
                if item.is_dir():
                    # 检查是否有postprocessor目录和配置文件
                    config_file = item / "postprocessor" / f"{item.name}.json"
                    if config_file.exists():
                        algorithms.append(item.name)

            logger.info(f"找到 {len(algorithms)} 个可用算法")
            return algorithms

        except Exception as e:
            logger.error(f"列出可用算法失败: {e}")
            return []

    @classmethod
    def list_available_algorithms(cls, platform: str = "KS968") -> List[str]:
        """
        列出可用的算法

        :param platform: 平台类型，默认KS968
        :return: 算法ID列表
        """
        try:
            platform_path = cls.ALGORITHM_BASE_PATH / platform
            if not platform_path.exists():
                logger.warning(f"平台目录不存在: {platform_path}")
                return []

            algorithms = []
            for item in platform_path.iterdir():
                if item.is_dir():
                    # 检查是否有postprocessor目录和配置文件
                    config_file = item / "postprocessor" / f"{item.name}.json"
                    if config_file.exists():
                        algorithms.append(item.name)

            logger.info(f"找到 {len(algorithms)} 个可用算法")
            return algorithms

        except Exception as e:
            logger.error(f"列出可用算法失败: {e}")
            return []

    @classmethod
    def get_algorithm_full_info(cls, algorithm_id: str, platform: str = "KS968") -> Optional[Dict[str, Any]]:
        """
        获取算法完整信息（包含元数据和配置）

        :param algorithm_id: 算法ID
        :param platform: 平台类型，默认KS968
        :return: 完整算法信息
        """
        try:
            # 获取元数据
            metadata = cls.get_algorithm_metadata_from_yaml(algorithm_id, platform)
            if not metadata:
                return None

            # 获取配置
            config_data = cls.get_algorithm_config_from_zhiquli(algorithm_id, platform)
            if not config_data:
                return None

            # 合并信息
            full_info = {
                'algorithm_id': algorithm_id,
                'algorithm_name': metadata.get('ch_name', algorithm_id),
                'algorithm_desc': metadata.get('desc', ''),
                'group_name': metadata.get('group_name', ''),
                'version': metadata.get('version', '1.0'),
                'process_time': metadata.get('process_time', 20),
                'model_info': metadata.get('model', {}),
                'alert_label': metadata.get('alert_label', []),
                'basic_params': config_data.get('basicParams', {}),
                'render_params': config_data.get('renderParams', {}),
                'platform': platform
            }

            return full_info

        except Exception as e:
            logger.error(f"获取算法完整信息失败: {algorithm_id}, 错误: {e}")
            return None

    @classmethod
    def _validate_zhiquli_config(cls, algorithm_id: str, zhiquli_config: Dict[str, Any], platform: str = "KS968") -> Dict[str, Any]:
        """
        验证智驱力配置参数

        :param algorithm_id: 算法ID
        :param zhiquli_config: 智驱力配置
        :param platform: 平台类型，默认KS968
        :return: 验证结果
        """
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }

        try:
            render_config = cls.get_algorithm_render_config(algorithm_id, platform)
            if not render_config:
                result['valid'] = False
                result['errors'].append(f"无法获取算法 {algorithm_id} 的配置信息")
                return result

            # 验证模型参数
            if 'model_args' in zhiquli_config and 'model_args' in render_config:
                cls._validate_config_section(
                    zhiquli_config['model_args'],
                    render_config['model_args'],
                    'model_args',
                    result
                )

            # 验证告警窗口参数
            if 'alert_window' in zhiquli_config and 'alert_window' in render_config:
                cls._validate_config_section(
                    zhiquli_config['alert_window'],
                    render_config['alert_window'],
                    'alert_window',
                    result
                )

            # 验证保留参数
            if 'reserved_args' in zhiquli_config and 'reserved_args' in render_config:
                cls._validate_config_section(
                    zhiquli_config['reserved_args'],
                    render_config['reserved_args'],
                    'reserved_args',
                    result
                )

        except Exception as e:
            result['valid'] = False
            result['errors'].append(f"验证配置时发生错误: {e}")

        return result

    @classmethod
    def _validate_config_section(cls, user_section: Dict[str, Any],
                                render_section: Dict[str, Any],
                                section_name: str,
                                result: Dict[str, Any]):
        """
        验证配置段
        """
        for key, user_value in user_section.items():
            if isinstance(user_value, dict):
                # 递归验证嵌套配置
                if key in render_section and isinstance(render_section[key], dict):
                    cls._validate_config_section(user_value, render_section[key], f"{section_name}.{key}", result)
            else:
                # 验证单个参数
                if key in render_section:
                    param_config = render_section[key]
                    if isinstance(param_config, dict):
                        param_type = param_config.get('type', 'text')
                        param_range = param_config.get('range', {})

                        # 验证数值范围
                        if param_type == 'number' and param_range:
                            min_val = param_range.get('min')
                            max_val = param_range.get('max')

                            if min_val is not None and user_value < min_val:
                                result['errors'].append(f"{section_name}.{key} 值 {user_value} 小于最小值 {min_val}")
                                result['valid'] = False

                            if max_val is not None and user_value > max_val:
                                result['errors'].append(f"{section_name}.{key} 值 {user_value} 大于最大值 {max_val}")
                                result['valid'] = False

                        # 验证选择项
                        if param_type == 'select' and 'options' in param_config:
                            valid_values = [option['value'] for option in param_config['options']]
                            if user_value not in valid_values:
                                result['errors'].append(f"{section_name}.{key} 值 {user_value} 不在有效选项中: {valid_values}")
                                result['valid'] = False

    @classmethod
    def _extract_schedule_config_from_user_config(cls, user_config: dict) -> dict:
        """
        从用户配置中提取调度配置参数

        :param user_config: 用户配置字典
        :return: 调度配置字典
        """
        # 只处理智驱力配置格式
        if isinstance(user_config, dict) and user_config.get('algorithm_type') == 'zhiquli':
            zhiquli_config = user_config.get('zhiquli_config', {})
            plan_config = zhiquli_config.get('plan', {})

            if plan_config:
                logger.info(f"提取智驱力调度配置: {plan_config}")
                return plan_config
            else:
                logger.warning("智驱力配置中缺少调度配置(plan)")
                return {}

        # 非智驱力配置返回空
        return {}

    @classmethod
    def _extract_user_config_from_user_config(cls, user_config: dict) -> dict:
        """
        从用户配置中提取用户自定义配置参数

        :param user_config: 用户配置字典
        :return: 用户自定义配置字典
        """
        # 只处理智驱力配置格式
        if isinstance(user_config, dict) and user_config.get('algorithm_type') == 'zhiquli':
            zhiquli_config = user_config.get('zhiquli_config', {})
            reserved_args = zhiquli_config.get('reserved_args', {})

            if reserved_args:
                logger.info(f"提取智驱力用户配置: {reserved_args}")
                return reserved_args
            else:
                logger.warning("智驱力配置中缺少用户配置(reserved_args)")
                return {}

        # 非智驱力配置返回空
        return {}
