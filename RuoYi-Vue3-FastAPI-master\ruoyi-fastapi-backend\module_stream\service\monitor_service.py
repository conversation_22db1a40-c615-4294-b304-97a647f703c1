from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any
from datetime import datetime, timedelta
from exceptions.exception import ServiceException
from module_stream.dao.task_dao import TaskDao
from module_stream.dao.stream_dao import StreamDao
from module_stream.entity.vo.task_vo import TaskPageQueryModel, TaskDetailModel
from module_stream.service.task_service import TaskService
from utils.log_util import logger


class MonitorService:
    """
    监控模块服务层
    """

    @classmethod
    async def get_monitor_tasks_services(
        cls, query_db: AsyncSession, query_object: TaskPageQueryModel, current_user_id: int
    ):
        """
        获取监控任务列表service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param current_user_id: 当前用户ID
        :return: 监控任务列表信息对象
        """
        # 使用任务服务获取任务列表
        task_list_result = await TaskService.get_task_list_services(
            query_db, query_object, current_user_id, is_page=True
        )

        return task_list_result

    @classmethod
    async def get_running_tasks_services(cls, query_db: AsyncSession, current_user_id: int):
        """
        获取运行中的任务列表service

        :param query_db: orm对象
        :param current_user_id: 当前用户ID
        :return: 运行中的任务列表
        """
        # 构建查询参数，只查询运行中的任务
        query_object = TaskPageQueryModel(
            status='1',  # 运行中状态
            page_num=1,
            page_size=100
        )
        
        # 获取运行中的任务
        running_tasks_result = await TaskService.get_task_list_services(
            query_db, query_object, current_user_id, is_page=False
        )

        return running_tasks_result

    @classmethod
    async def get_task_status_services(cls, query_db: AsyncSession, task_id: int, current_user_id: int):
        """
        获取任务状态信息service

        :param query_db: orm对象
        :param task_id: 任务ID
        :param current_user_id: 当前用户ID
        :return: 任务状态信息
        """
        # 获取任务详情
        task_detail = await TaskService.detail_task_services(query_db, task_id)
        
        # 验证用户权限（通过视频流的用户ID）
        stream_info = await StreamDao.get_stream_detail_by_id(query_db, task_detail.stream_id)
        if not stream_info or stream_info.user_id != current_user_id:
            raise ServiceException(message='无权限访问该任务')

        # 构建状态信息
        status_info = {
            'task_id': task_detail.task_id,
            'task_name': task_detail.task_name,
            'status': task_detail.status,
            'status_text': task_detail.status_text,
            'run_count': task_detail.run_count,
            'alert_count': task_detail.alert_count,
            'error_count': task_detail.error_count,
            'last_run_time': task_detail.last_run_time,
            'stream_name': task_detail.stream_name,
            'stream_url': task_detail.stream_url,
            'stream_status': task_detail.stream_status,
            'algorithm_name': task_detail.algorithm_name,
            'can_start': task_detail.can_start,
            'can_stop': task_detail.can_stop,
            'can_pause': task_detail.can_pause
        }

        return status_info

    @classmethod
    async def get_monitor_statistics_services(cls, query_db: AsyncSession, current_user_id: int):
        """
        获取监控统计信息service

        :param query_db: orm对象
        :param current_user_id: 当前用户ID
        :return: 监控统计信息
        """
        # 获取所有任务
        all_tasks_query = TaskPageQueryModel(page_num=1, page_size=1000)
        all_tasks_result = await TaskService.get_task_list_services(
            query_db, all_tasks_query, current_user_id, is_page=False
        )

        # 统计各种状态的任务数量
        total_tasks = len(all_tasks_result)
        running_tasks = len([task for task in all_tasks_result if task.status == '1'])
        stopped_tasks = len([task for task in all_tasks_result if task.status == '0'])
        paused_tasks = len([task for task in all_tasks_result if task.status == '2'])

        # 统计今日告警数量
        today = datetime.now().date()
        today_alerts = sum([task.alert_count or 0 for task in all_tasks_result])

        # 统计活跃任务（最近24小时有运行记录的任务）
        yesterday = datetime.now() - timedelta(days=1)
        active_tasks = len([
            task for task in all_tasks_result 
            if task.last_run_time and task.last_run_time > yesterday
        ])

        statistics = {
            'total_tasks': total_tasks,
            'running_tasks': running_tasks,
            'stopped_tasks': stopped_tasks,
            'paused_tasks': paused_tasks,
            'active_tasks': active_tasks,
            'today_alerts': today_alerts,
            'task_status_distribution': {
                'running': running_tasks,
                'stopped': stopped_tasks,
                'paused': paused_tasks
            }
        }

        return statistics

    @classmethod
    async def batch_start_tasks_services(
        cls, query_db: AsyncSession, task_ids: List[int], current_user_id: int
    ):
        """
        批量启动任务service

        :param query_db: orm对象
        :param task_ids: 任务ID列表
        :param current_user_id: 当前用户ID
        :return: 批量启动结果
        """
        success_count = 0
        failed_tasks = []

        for task_id in task_ids:
            try:
                # 验证任务权限
                task_info = await TaskDao.get_task_detail_by_id(query_db, task_id)
                if not task_info:
                    failed_tasks.append({'task_id': task_id, 'error': '任务不存在'})
                    continue

                # 验证用户权限
                stream_info = await StreamDao.get_stream_detail_by_id(query_db, task_info.stream_id)
                if not stream_info or stream_info.user_id != current_user_id:
                    failed_tasks.append({'task_id': task_id, 'error': '无权限访问该任务'})
                    continue

                # 启动任务 - 使用正确的TaskExecutionService
                from module_stream.service.task_execution_service import TaskExecutionService
                await TaskExecutionService.start_task(query_db, task_id, current_user_id)
                success_count += 1

            except Exception as e:
                failed_tasks.append({'task_id': task_id, 'error': str(e)})
                logger.error(f'启动任务 {task_id} 失败: {e}')

        result = {
            'success_count': success_count,
            'failed_count': len(failed_tasks),
            'total_count': len(task_ids),
            'failed_tasks': failed_tasks
        }

        return result

    @classmethod
    async def batch_stop_tasks_services(
        cls, query_db: AsyncSession, task_ids: List[int], current_user_id: int
    ):
        """
        批量停止任务service

        :param query_db: orm对象
        :param task_ids: 任务ID列表
        :param current_user_id: 当前用户ID
        :return: 批量停止结果
        """
        success_count = 0
        failed_tasks = []

        for task_id in task_ids:
            try:
                # 验证任务权限
                task_info = await TaskDao.get_task_detail_by_id(query_db, task_id)
                if not task_info:
                    failed_tasks.append({'task_id': task_id, 'error': '任务不存在'})
                    continue

                # 验证用户权限
                stream_info = await StreamDao.get_stream_detail_by_id(query_db, task_info.stream_id)
                if not stream_info or stream_info.user_id != current_user_id:
                    failed_tasks.append({'task_id': task_id, 'error': '无权限访问该任务'})
                    continue

                # 停止任务 - 使用正确的TaskExecutionService
                from module_stream.service.task_execution_service import TaskExecutionService
                await TaskExecutionService.stop_task(query_db, task_id, current_user_id)
                success_count += 1

            except Exception as e:
                failed_tasks.append({'task_id': task_id, 'error': str(e)})
                logger.error(f'停止任务 {task_id} 失败: {e}')

        result = {
            'success_count': success_count,
            'failed_count': len(failed_tasks),
            'total_count': len(task_ids),
            'failed_tasks': failed_tasks
        }

        return result
