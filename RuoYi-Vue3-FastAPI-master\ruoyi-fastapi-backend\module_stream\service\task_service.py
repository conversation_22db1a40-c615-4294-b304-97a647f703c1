from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from datetime import datetime
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_stream.dao.task_dao import TaskDao
from module_stream.dao.stream_dao import StreamDao
from module_stream.entity.do.stream_do import SurveillanceStream
from module_stream.entity.vo.task_vo import DeleteTaskModel, TaskModel, TaskPageQueryModel, TaskDetailModel
from utils.common_util import CamelCaseUtil
from utils.excel_util import ExcelUtil
from utils.log_util import logger
from sqlalchemy import select
import json


class TaskService:
    """
    检测任务模块服务层
    """

    @classmethod
    async def get_task_list_services(
        cls, query_db: AsyncSession, query_object: TaskPageQueryModel, current_user_id: int, is_page: bool = False
    ):
        """
        获取检测任务列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param current_user_id: 当前用户ID
        :param is_page: 是否开启分页
        :return: 检测任务列表信息对象
        """
        from utils.log_util import logger
        logger.info(f"=== TaskService.get_task_list_services ===")
        logger.info(f"当前用户ID: {current_user_id}")
        logger.info(f"查询参数: {query_object}")
        logger.info(f"是否分页: {is_page}")

        # 通过关联视频流表来过滤用户权限
        task_list_result = await TaskDao.get_task_list_by_user(query_db, query_object, current_user_id, is_page)

        logger.info(f"DAO查询结果类型: {type(task_list_result)}")
        if hasattr(task_list_result, 'total'):
            logger.info(f"分页结果: total={task_list_result.total}, rows={len(task_list_result.rows)}")
        else:
            logger.info(f"非分页结果: {len(task_list_result) if task_list_result else 0} 条记录")

        # 如果是分页查询，需要获取详细信息
        if is_page and hasattr(task_list_result, 'rows'):
            logger.info(f"开始构建分页任务详情，任务数量: {len(task_list_result.rows)}")
            detailed_tasks = []
            for i, task in enumerate(task_list_result.rows):
                try:
                    detailed_task = await cls._build_task_detail(query_db, task)
                    detailed_tasks.append(detailed_task)
                    logger.info(f"任务 {i+1} 详情构建成功: {detailed_task.task_name}")
                except Exception as e:
                    logger.error(f"任务 {i+1} 详情构建失败: {e}")
                    raise
            task_list_result.rows = detailed_tasks
            logger.info(f"分页任务详情构建完成，最终数量: {len(detailed_tasks)}")
        elif not is_page:
            # 非分页查询
            logger.info(f"开始构建非分页任务详情，任务数量: {len(task_list_result)}")
            detailed_tasks = []
            for i, task in enumerate(task_list_result):
                try:
                    detailed_task = await cls._build_task_detail(query_db, task)
                    detailed_tasks.append(detailed_task)
                    logger.info(f"任务 {i+1} 详情构建成功: {detailed_task.task_name}")
                except Exception as e:
                    logger.error(f"任务 {i+1} 详情构建失败: {e}")
                    raise
            task_list_result = detailed_tasks
            logger.info(f"非分页任务详情构建完成，最终数量: {len(detailed_tasks)}")

        logger.info(f"=== TaskService.get_task_list_services 完成 ===")
        return task_list_result

    @classmethod
    async def _build_task_detail(cls, query_db: AsyncSession, task) -> TaskDetailModel:
        """
        构建任务详情信息

        :param query_db: orm对象
        :param task: 任务对象（可能是ORM对象或字典）
        :return: 任务详情
        """
        # 处理任务数据（兼容ORM对象和字典）
        if isinstance(task, dict):
            # 字典格式（来自CamelCaseUtil转换）
            task_id = task.get('taskId') or task.get('task_id')
            task_name = task.get('taskName') or task.get('task_name')
            stream_id = task.get('streamId') or task.get('stream_id')
            status = task.get('status')
            run_count = task.get('runCount') or task.get('run_count')
            alert_count = task.get('alertCount') or task.get('alert_count')
            error_count = task.get('errorCount') or task.get('error_count')
            last_run_time = task.get('lastRunTime') or task.get('last_run_time')
            create_time = task.get('createTime') or task.get('create_time')
            remark = task.get('remark')
            algorithm_id = task.get('algorithmId') or task.get('algorithm_id')
            algorithm_name = task.get('algorithmName') or task.get('algorithm_name')
            algorithm_version = task.get('algorithmVersion') or task.get('algorithm_version')
            user_config = task.get('userConfig') or task.get('user_config')
        else:
            # ORM对象格式
            task_id = task.task_id
            task_name = task.task_name
            stream_id = task.stream_id
            status = task.status
            run_count = task.run_count
            alert_count = task.alert_count
            error_count = task.error_count
            last_run_time = task.last_run_time
            create_time = task.create_time
            remark = task.remark
            algorithm_id = task.algorithm_id
            algorithm_name = task.algorithm_name
            algorithm_version = task.algorithm_version
            user_config = task.user_config

        # 获取视频流信息
        stream_info = await StreamDao.get_stream_detail_by_id(query_db, stream_id)

        # 获取任务的算法配置（现在直接从任务表获取）
        algorithm_display_name = algorithm_name or algorithm_id or "未配置"
        has_algorithm_config = bool(user_config and algorithm_id)

        # 构建状态描述和操作权限
        status_text = cls._get_status_text(status)
        can_start = status in ['0', '2'] and has_algorithm_config
        can_stop = status == '1'
        can_pause = status == '1'

        # 确保必需字段不为None
        if task_id is None:
            raise ServiceException(message='任务ID不能为空')

        # 构建任务详情数据
        task_detail_data = {
            "task_id": task_id,
            "task_name": task_name or "未命名任务",
            "status": status or "0",
            "run_count": run_count or 0,
            "alert_count": alert_count or 0,
            "error_count": error_count or 0,
            "last_run_time": last_run_time,
            "create_time": create_time,
            "remark": remark,
            "stream_name": stream_info.stream_name if stream_info else "未知视频流",
            "stream_url": stream_info.rtsp_url if stream_info else "",
            "stream_status": stream_info.status if stream_info else "0",
            "stream_location": stream_info.location if stream_info else None,
            "algorithm_id": algorithm_id,
            "algorithm_name": algorithm_display_name,
            "algorithm_version": algorithm_version,
            "has_algorithm_config": has_algorithm_config,
            "status_text": status_text,
            "can_start": can_start,
            "can_stop": can_stop,
            "can_pause": can_pause
        }

        return TaskDetailModel(**task_detail_data)

    @classmethod
    def _get_status_text(cls, status: str) -> str:
        """获取状态文本描述"""
        status_map = {
            '0': '已停止',
            '1': '运行中',
            '2': '已暂停'
        }
        return status_map.get(status, '未知状态')



    @classmethod
    async def delete_task_services(cls, query_db: AsyncSession, page_object: DeleteTaskModel):
        """
        删除检测任务信息service

        :param query_db: orm对象
        :param page_object: 删除检测任务对象
        :return: 删除检测任务校验结果
        """
        logger.info(f"=== TaskService.delete_task_services ===")
        logger.info(f"删除请求对象: {page_object}")
        logger.info(f"任务ID字符串: {page_object.task_ids}")

        if page_object.task_ids:
            task_id_list = page_object.task_ids.split(',')
            logger.info(f"解析后的任务ID列表: {task_id_list}")

            # 检查是否有运行中的任务
            for task_id in task_id_list:
                try:
                    task_info = await TaskDao.get_task_detail_by_id(query_db, int(task_id))
                    if task_info:
                        logger.info(f"任务 {task_id} 信息: 名称={task_info.task_name}, 状态={task_info.status}")
                        if task_info.status == '1':
                            raise ServiceException(message=f'任务 {task_info.task_name} 正在运行中，请先停止任务')
                    else:
                        logger.warning(f"任务 {task_id} 不存在")
                except ValueError as e:
                    logger.error(f"任务ID {task_id} 格式错误: {e}")
                    raise ServiceException(message=f'任务ID格式错误: {task_id}')

            # 执行删除操作
            result = await TaskDao.delete_task_dao(query_db, task_id_list)
            logger.info(f"删除操作完成，影响行数: {result}")
        else:
            logger.warning("没有提供要删除的任务ID")
            raise ServiceException(message='没有提供要删除的任务ID')

    @classmethod
    async def detail_task_services(cls, query_db: AsyncSession, task_id: int):
        """
        获取检测任务详细信息service

        :param query_db: orm对象
        :param task_id: 检测任务id
        :return: 检测任务id对应的信息
        """
        task_info = await TaskDao.get_task_detail_by_id(query_db, task_id)
        if task_info:
            result = await cls._build_task_detail(query_db, task_info)
        else:
            raise ServiceException(message='任务不存在')

        return result

    @classmethod
    async def start_task_services(cls, query_db: AsyncSession, task_id: int):
        """
        启动检测任务service

        :param query_db: orm对象
        :param task_id: 任务ID
        :return: 启动结果
        """
        task_info = await TaskDao.get_task_detail_by_id(query_db, task_id)
        if not task_info:
            raise ServiceException(message='任务不存在')
        
        if task_info.status == '1':
            raise ServiceException(message='任务已在运行中')
        
        # 检查视频流是否存在且启用
        stream_info = await StreamDao.get_stream_detail_by_id(query_db, task_info.stream_id)
        if not stream_info or stream_info.status != '1':
            raise ServiceException(message='视频流不存在或未启用')
        
        # 检查任务的算法配置
        if not task_info.algorithm_id:
            raise ServiceException(message='任务未配置算法，无法启动')

        if not task_info.user_config:
            raise ServiceException(message='算法参数未配置，无法启动')
        
        # 更新任务状态
        await TaskDao.update_task_status(
            query_db, 
            task_id, 
            '1',
            last_run_time=datetime.now(),
            run_count=(task_info.run_count or 0) + 1
        )
        
        # 注意：实际的任务启动现在由 TaskExecutionService 处理
        # 这里只是更新数据库状态，实际启动逻辑在 MonitorController 中调用 TaskExecutionService
        logger.info(f"任务 {task_id} 状态更新完成，实际启动由 TaskExecutionService 处理")

        return True

    @classmethod
    async def stop_task_services(cls, query_db: AsyncSession, task_id: int):
        """
        停止检测任务service

        :param query_db: orm对象
        :param task_id: 任务ID
        :return: 停止结果
        """
        task_info = await TaskDao.get_task_detail_by_id(query_db, task_id)
        if not task_info:
            raise ServiceException(message='任务不存在')
        
        if task_info.status == '0':
            raise ServiceException(message='任务已停止')
        
        # 更新任务状态
        await TaskDao.update_task_status(query_db, task_id, '0')
        
        # 注意：实际的任务停止现在由 TaskExecutionService 处理
        # 这里只是更新数据库状态，实际停止逻辑在 MonitorController 中调用 TaskExecutionService
        logger.info(f"任务 {task_id} 状态更新完成，实际停止由 TaskExecutionService 处理")

        return True

    @classmethod
    async def pause_task_services(cls, query_db: AsyncSession, task_id: int):
        """
        暂停检测任务service

        :param query_db: orm对象
        :param task_id: 任务ID
        :return: 暂停结果
        """
        task_info = await TaskDao.get_task_detail_by_id(query_db, task_id)
        if not task_info:
            raise ServiceException(message='任务不存在')
        
        if task_info.status != '1':
            raise ServiceException(message='只能暂停运行中的任务')
        
        # 更新任务状态
        await TaskDao.update_task_status(query_db, task_id, '2')
        
        # TODO: 这里应该调用实际的算法暂停逻辑
        
        return True
