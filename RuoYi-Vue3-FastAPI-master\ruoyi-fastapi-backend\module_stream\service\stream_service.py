from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
import os
import cv2
import asyncio
import base64
import numpy as np
import time
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_stream.dao.stream_dao import StreamDao
from module_stream.entity.vo.stream_vo import DeleteStreamModel, StreamModel, StreamPageQueryModel
from utils.common_util import CamelCaseUtil
from utils.excel_util import ExcelUtil


class StreamService:
    """
    视频流管理模块服务层
    """

    @classmethod
    async def get_stream_list_services(
        cls, query_db: AsyncSession, query_object: StreamPageQueryModel, current_user_id: int, is_page: bool = False
    ):
        """
        获取视频流管理列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param current_user_id: 当前用户ID
        :param is_page: 是否开启分页
        :return: 视频流管理列表信息对象
        """
        # 添加用户权限过滤 - 使用user_id字段
        query_object.user_id = current_user_id
        stream_list_result = await StreamDao.get_stream_list(query_db, query_object, is_page)

        return stream_list_result


    @classmethod
    async def add_stream_services(cls, query_db: AsyncSession, page_object: StreamModel):
        """
        新增视频流管理信息service

        :param query_db: orm对象
        :param page_object: 新增视频流管理对象
        :return: 新增视频流管理校验结果
        """
        # 检查流名称是否已存在（在当前用户范围内）
        existing_stream_by_name = await StreamDao.get_stream_detail_by_info(
            query_db, StreamModel(user_id=page_object.user_id, stream_name=page_object.stream_name)
        )
        if existing_stream_by_name:
            raise ServiceException(message='流名称已存在')

        # 检查RTSP地址是否已存在（在当前用户范围内）
        existing_stream_by_url = await StreamDao.get_stream_detail_by_info(
            query_db, StreamModel(user_id=page_object.user_id, rtsp_url=page_object.rtsp_url)
        )
        if existing_stream_by_url:
            raise ServiceException(message='RTSP地址已存在')

        try:
            # 设置默认值
            if page_object.del_flag is None:
                page_object.del_flag = '0'
            if page_object.status is None:
                page_object.status = '1'
            if page_object.is_recording is None:
                page_object.is_recording = 0

            await StreamDao.add_stream_dao(query_db, page_object)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            # 处理数据库完整性约束错误
            error_str = str(e)
            if 'uk_user_stream_name' in error_str and 'Duplicate entry' in error_str:
                raise ServiceException(message='流名称已存在')
            elif 'uk_user_rtsp_url' in error_str and 'Duplicate entry' in error_str:
                raise ServiceException(message='RTSP地址已存在')
            else:
                raise e

    @classmethod
    async def edit_stream_services(cls, query_db: AsyncSession, page_object: StreamModel):
        """
        编辑视频流管理信息service

        :param query_db: orm对象
        :param page_object: 编辑视频流管理对象
        :return: 编辑视频流管理校验结果
        """
        stream_info = await cls.stream_detail_services(query_db, page_object.stream_id)
        if not stream_info.stream_id:
            raise ServiceException(message='视频流管理不存在')

        # 检查流名称是否已被其他记录使用
        if page_object.stream_name and page_object.stream_name != stream_info.stream_name:
            existing_stream_by_name = await StreamDao.get_stream_detail_by_info(
                query_db, StreamModel(user_id=stream_info.user_id, stream_name=page_object.stream_name, stream_id=page_object.stream_id)
            )
            if existing_stream_by_name:
                raise ServiceException(message='流名称已存在')

        # 检查RTSP地址是否已被其他记录使用
        if page_object.rtsp_url and page_object.rtsp_url != stream_info.rtsp_url:
            existing_stream_by_url = await StreamDao.get_stream_detail_by_info(
                query_db, StreamModel(user_id=stream_info.user_id, rtsp_url=page_object.rtsp_url, stream_id=page_object.stream_id)
            )
            if existing_stream_by_url:
                raise ServiceException(message='RTSP地址已存在')

        edit_stream = page_object.model_dump(exclude_unset=True, exclude={'del_flag', 'create_by', 'create_time', })
        try:
            await StreamDao.edit_stream_dao(query_db, edit_stream)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='更新成功')
        except Exception as e:
            await query_db.rollback()
            # 处理数据库完整性约束错误
            error_str = str(e)
            if 'uk_user_stream_name' in error_str and 'Duplicate entry' in error_str:
                raise ServiceException(message='流名称已存在')
            elif 'uk_user_rtsp_url' in error_str and 'Duplicate entry' in error_str:
                raise ServiceException(message='RTSP地址已存在')
            else:
                raise e

    @classmethod
    async def delete_stream_services(cls, query_db: AsyncSession, page_object: DeleteStreamModel, user_id: int):
        """
        删除视频流管理信息service

        :param query_db: orm对象
        :param page_object: 删除视频流管理对象
        :param user_id: 当前用户ID
        :return: 删除视频流管理校验结果
        """
        if page_object.stream_ids:
            stream_id_list = page_object.stream_ids.split(',')
            deleted_count = 0
            try:
                for stream_id in stream_id_list:
                    # 先检查视频流是否存在且属于当前用户
                    stream_info = await StreamDao.get_stream_detail_by_id(query_db, int(stream_id))
                    if not stream_info:
                        raise ServiceException(message=f'视频流ID {stream_id} 不存在')
                    if stream_info.user_id != user_id:
                        raise ServiceException(message=f'无权限删除视频流ID {stream_id}')

                    # 执行删除并检查结果
                    # 直接传递stream_id而不是通过StreamModel
                    rowcount = await StreamDao.delete_stream_dao(query_db, int(stream_id))
                    if rowcount > 0:
                        deleted_count += 1
                    else:
                        raise ServiceException(message=f'删除视频流ID {stream_id} 失败')

                await query_db.commit()

                if deleted_count == len(stream_id_list):
                    return CrudResponseModel(is_success=True, message=f'成功删除 {deleted_count} 个视频流')
                else:
                    return CrudResponseModel(is_success=False, message=f'部分删除失败，成功删除 {deleted_count}/{len(stream_id_list)} 个视频流')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='传入流ID为空')

    @classmethod
    async def stream_detail_services(cls, query_db: AsyncSession, stream_id: int):
        """
        获取视频流管理详细信息service

        :param query_db: orm对象
        :param stream_id: 流ID
        :return: 流ID对应的信息
        """
        stream = await StreamDao.get_stream_detail_by_id(query_db, stream_id=stream_id)
        if stream:
            result = StreamModel(**CamelCaseUtil.transform_result(stream))
        else:
            result = StreamModel(**dict())

        return result

    @staticmethod
    async def export_stream_list_services(stream_list: List):
        """
        导出视频流管理信息service

        :param stream_list: 视频流管理信息列表
        :return: 视频流管理信息对应excel的二进制数据
        """
        # 创建一个映射字典，将英文键映射到中文键
        mapping_dict = {
            'streamId': '流ID',
            'streamName': '流名称',
            'rtspUrl': 'RTSP地址',
            'location': '安装位置',
            'streamConfig': '流配置参数',
            'status': '状态(0停用 1启用)',
            'isRecording': '是否录制',
            'delFlag': '删除标志',
            'createBy': '创建者',
            'createTime': '创建时间',
            'updateBy': '更新者',
            'updateTime': '更新时间',
            'remark': '备注',
        }
        binary_data = ExcelUtil.export_list2excel(stream_list, mapping_dict)

        return binary_data

    @classmethod
    async def test_rtsp_connection_services(cls, rtsp_url: str, timeout: int = 5):
        """
        测试RTSP连接service

        :param rtsp_url: RTSP地址
        :param timeout: 超时时间（秒）
        :return: 测试结果
        """
        try:
            # 设置OpenCV环境变量
            os.environ["OPENCV_FFMPEG_CAPTURE_OPTIONS"] = "rtsp_transport;tcp"
            os.environ["OPENCV_LOG_LEVEL"] = "ERROR"

            # 在线程池中运行阻塞的OpenCV操作
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, cls._test_rtsp_sync, rtsp_url, timeout)

            return result
        except Exception as e:
            # 使用友好的错误信息
            error_message = cls._get_friendly_error_message(str(e))
            return {
                'success': False,
                'message': error_message,
                'data': None
            }

    @staticmethod
    def _test_rtsp_sync(rtsp_url: str, timeout: int = 5):
        """
        同步测试RTSP连接

        :param rtsp_url: RTSP地址
        :param timeout: 超时时间（秒）
        :return: 测试结果
        """
        cap = None
        try:
            # 创建VideoCapture对象
            cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)

            # 设置超时
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout * 1000)
            cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, timeout * 1000)

            if not cap.isOpened():
                return {
                    'success': False,
                    'message': '无法连接到视频流，请检查RTSP地址是否正确',
                    'data': None
                }

            # 尝试读取一帧
            ret, frame = cap.read()
            if not ret or frame is None:
                return {
                    'success': False,
                    'message': '视频流连接成功但无法获取画面，请检查摄像头是否正常工作',
                    'data': None
                }

            # 获取视频流信息
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)

            # 将帧转换为base64编码的JPEG
            _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
            frame_base64 = base64.b64encode(buffer).decode('utf-8')

            return {
                'success': True,
                'message': 'RTSP连接测试成功，视频流正常',
                'data': {
                    'width': width,
                    'height': height,
                    'fps': fps if fps > 0 else 0,  # 如果无法获取fps，默认0
                    'frame': frame_base64,
                    'timestamp': time.time()
                }
            }

        except Exception as e:
            # 根据错误类型提供友好的错误信息
            error_message = StreamService._get_friendly_error_message(str(e))
            return {
                'success': False,
                'message': error_message,
                'data': None
            }
        finally:
            if cap is not None:
                cap.release()

    @classmethod
    def _get_friendly_error_message(cls, error_str: str) -> str:
        """
        将技术错误信息转换为用户友好的提示信息

        :param error_str: 原始错误信息
        :return: 友好的错误提示
        """
        error_lower = error_str.lower()

        # 超时相关错误
        if 'timeout' in error_lower or 'timed out' in error_lower:
            return '连接超时，请检查网络连接或RTSP地址是否可达'

        # 连接被拒绝
        if 'connection refused' in error_lower or 'refused' in error_lower:
            return '连接被拒绝，请检查RTSP服务是否启动或端口是否正确'

        # 认证失败
        if 'unauthorized' in error_lower or '401' in error_lower:
            return '认证失败，请检查用户名和密码是否正确'

        # 地址不可达
        if 'unreachable' in error_lower or 'no route' in error_lower:
            return '无法访问指定地址，请检查IP地址和网络连接'

        # DNS解析失败
        if 'name resolution' in error_lower or 'dns' in error_lower:
            return '域名解析失败，请检查域名是否正确或使用IP地址'

        # 404错误
        if '404' in error_lower or 'not found' in error_lower:
            return '视频流路径不存在，请检查RTSP路径是否正确'

        # 协议错误
        if 'protocol' in error_lower:
            return 'RTSP协议错误，请检查地址格式是否正确'

        # 权限错误
        if 'permission' in error_lower or 'forbidden' in error_lower:
            return '访问权限不足，请检查用户权限设置'

        # 网络错误
        if 'network' in error_lower or 'connection' in error_lower:
            return '网络连接异常，请检查网络设置'

        # 格式错误
        if 'format' in error_lower or 'codec' in error_lower:
            return '视频格式不支持，请检查摄像头编码设置'

        # 默认错误信息
        return '连接测试失败，请检查RTSP地址、网络连接和设备状态'

    @classmethod
    async def get_rtsp_frame_services(cls, rtsp_url: str, timeout: int = 5):
        """
        获取RTSP视频流单帧service

        :param rtsp_url: RTSP地址
        :param timeout: 超时时间（秒）
        :return: 视频帧的base64编码
        """
        try:
            # 设置OpenCV环境变量
            os.environ["OPENCV_FFMPEG_CAPTURE_OPTIONS"] = "rtsp_transport;tcp"
            os.environ["OPENCV_LOG_LEVEL"] = "ERROR"

            # 在线程池中运行阻塞的OpenCV操作
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, cls._get_rtsp_frame_sync, rtsp_url, timeout)

            return result
        except Exception as e:
            # 使用友好的错误信息
            error_message = cls._get_friendly_error_message(str(e))
            return {
                'success': False,
                'message': error_message,
                'frame': None
            }

    @staticmethod
    def _get_rtsp_frame_sync(rtsp_url: str, timeout: int = 5):
        """
        同步获取RTSP视频流单帧

        :param rtsp_url: RTSP地址
        :param timeout: 超时时间（秒）
        :return: 视频帧结果
        """
        cap = None
        try:
            # 创建VideoCapture对象
            cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)

            # 设置超时
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout * 1000)
            cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, timeout * 1000)

            if not cap.isOpened():
                return {
                    'success': False,
                    'message': '无法连接到视频流，请检查RTSP地址是否正确',
                    'frame': None
                }

            # 尝试读取一帧
            ret, frame = cap.read()
            if not ret or frame is None:
                return {
                    'success': False,
                    'message': '视频流连接成功但无法获取画面，请检查摄像头是否正常工作',
                    'frame': None
                }

            # 调整图像大小以减少传输数据量
            height, width = frame.shape[:2]
            if width > 640:
                scale = 640 / width
                new_width = 640
                new_height = int(height * scale)
                frame = cv2.resize(frame, (new_width, new_height))

            # 将帧转换为base64编码的JPEG
            _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 70])
            frame_base64 = base64.b64encode(buffer).decode('utf-8')

            return {
                'success': True,
                'message': '获取视频帧成功',
                'frame': frame_base64
            }

        except Exception as e:
            # 使用友好的错误信息
            from module_stream.service.stream_service import StreamService
            error_message = StreamService._get_friendly_error_message(str(e))
            return {
                'success': False,
                'message': error_message,
                'frame': None
            }
        finally:
            if cap is not None:
                cap.release()
